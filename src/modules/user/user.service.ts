/* eslint-disable class-methods-use-this */
import * as B<PERSON><PERSON>rom<PERSON> from 'bluebird';
import * as bcrypt from 'bcryptjs';
import { I18nService } from 'nestjs-i18n';
import {
  BadGatewayException,
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { PaymentService } from '../subscription/services/payment.service';
import { BullhornIntegrationService } from '../jobs/service/bullhorn-integration.service';
import { UserRepository } from './repositories/user.repository';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { LanguageCode, StatusCode } from '../../common/constants/common.constant';
import {
  FindOneOptions,
  FindOptionsWhere,
  FindOptionsRelations,
  FindOptionsRelationByString,
  DataSource,
  Query<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  More<PERSON><PERSON><PERSON>rEqual,
  Not,
} from 'typeorm';
import { LicenseType, UnipileAccountStatus, UserEntity, UserStatus } from './entities/user.entity';
import { LoginDto } from '../auth/dto/login-user.dto';
import { SendByEmailItemDto, SendByEmailsDto } from '../mail/dto/send-by-emails.dto';
import { MailService } from '../mail/mail.service';
import { RoleRepository } from './repositories/role.repository';
import { RoleEntity, RoleEnum } from './entities/role.entity';
import { UpdateMeRequestDto, UpdateUserDto, UpdateUserPermissionDto } from './dto/update-user.dto';
import { UserPermissionEntity } from './entities/user-permission.entity';
import { PermissionEntity } from './entities/permission.entity';
import { OrganizationEntity } from './entities/organization.entity';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';
import { JobLead } from '../jobs/entities/job-leads.entity';
import {
  QuickCreateUserDto,
  UpdateBullhornConfigDto,
  UpsertWorkingTimeDto,
  GetDashboardMetricsQuery,
} from './dto/user.dto';
import { UpdateSolutionsDto } from './dto/update-consultant.dto';
import { UserSearchDto } from './dto/user-search.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { CompleteOnboardingDto } from './dto/complete-onboarding.dto';
import { SequenceRepository } from '../mail/repositories/sequence.repostiory';
import { SequenceEntity, SequenceStatus } from '../mail/entities/sequence.entity';
import { SequenceStepEntity, SequenceStepType } from '../mail/entities/sequence-step.entity';
import {
  SequenceInstanceEntity,
  SequenceStepStatus,
} from '../mail/entities/sequence-instance.entity';
import {
  CreateDocumentFileDto,
  CreateOrganizationDto,
  OnboardingCompanyDto,
  OnboardingUserDto,
  SingleOnboardingUserDto,
  SubscriptionOptionsDto,
} from './dto/create-origanization.dto';
import { FileUploadService } from '../files-upload/file-upload.service';
import { v4 as uuid } from 'uuid';
import {
  LinkedConnectionDto,
  UnipileResendCheckpointDto,
  UnipileSolveCheckpointDto,
  UnipileUpdateAccountInApp,
} from './dto/linked-connection.dto';
import {
  APP_CONFIG,
  DefaultRoleIds,
  SendGridConfig,
  unipileConfig,
} from 'src/configs/configs.constants';
import * as unipileClient from '../jobs/utils/unipile-service.utils';
import * as sgMail from '@sendgrid/mail';
import { SequenceInstanceRepository } from '../mail/repositories/sequence-instance.repository';
import { stat, writeFileSync } from 'fs';
import { NotificationRepository } from '../notification/repositories/notification.repository';
import { NotificationEnum } from '../notification/entities/notification.entity';
import { SequenceActivityLogRepository } from '../mail/repositories/sequence-activity-log.repository';
import {
  SequenceActivityLogEntity,
  SequenceActivityType,
} from '../mail/entities/sequence-activity-log.entity';
import { JobLeadStatsDto } from '../jobs/dto/job-lead/job-lead-stats.dto';
import { UserWorkingTimeRepository } from './repositories/user-working-time.repository';
import { UserWorkingTimeEntity } from './entities/user-working-time.entity';
import * as moment from 'moment-timezone';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import {
  convertArrayToObject,
  decodePassword,
  UserStatusByOrgStatus,
} from 'src/common/utils/helpers.util';
import { UserLimitEntity } from './entities/user-limit.entity';
import { UserLimitType } from './enums/user-limit.enum';
import { OrganizationStatusEnum } from './enums/organization.enum';
import { NotificationService } from '../notification/services/notification.service';
import { calculateUserFeatures } from 'src/common/utils/helpers.util';
import { SubscriptionService } from '../subscription/services/subscription.service';
import {
  SubscriptionEntity,
  SubscriptionStatus,
} from '../subscription/entities/subscription.entity';
import { QuotaService } from '../subscription/services/quota.service';
import { CreditService } from '../subscription/services/credit.service';
import { LicenseService } from '../subscription/services/license.service';
import { FEATURES, FeatureUnitEnum } from '../../configs/features.config';
import { PLAN_FEATURES } from '../../configs/plan-features.config';
import { PLANS } from '../../configs/plans.config';
import {
  QuotaUsageLogEntity,
  QuotaUsageSource,
  PerformedByType,
  QuotaLogType,
} from '../subscription/entities/quota-usage-log.entity';
import {
  OrganizationQuotaEntity,
  QuotaSource,
} from '../subscription/entities/organization-quota.entity';
import { UserQuotaRepository } from '../subscription/repositories/user-quota.repository';
import { UpsertCreditManagementDto } from './dto/upsert-credit-management.dto';
import { TopupCreditsDto } from './dto/topup-credits.dto';
import { OrganizationQuotaRepository } from '../subscription/repositories/organization-quota.repository';
import { SequenceStepTaskEntity } from '../mail/entities/sequence-step-task.entity';
import { FoundContactEntity } from '../employee-finder/entities/found-contact.entity';
import { ContactEntity } from './entities/contact.entity';
import { FoundCompanyEntity } from '../employee-finder/entities/found-company.entity';
import { CrmCompanyEntity } from '../crm/entities/crm-company.entity';
import {
  calculateSavings,
  getAllTopupPackages,
  getTopupPackage,
  TopupPackageEnum,
} from './constants/topup-packages.constant';

@Injectable()
export class UserService extends BaseAbstractService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly userRepository: UserRepository,
    readonly i18nService: I18nService,
    private readonly mailService: MailService,
    private readonly roleRepository: RoleRepository,
    private readonly dataSource: DataSource,
    private fileUploadService: FileUploadService,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly notificationRepository: NotificationRepository,
    private readonly userWorkingTimeRepository: UserWorkingTimeRepository,
    private readonly linkedinFinderService: LinkedInFinderService,
    private readonly notificationService: NotificationService,
    private readonly subscriptionService: SubscriptionService,
    private readonly quotaService: QuotaService,
    private readonly userQuotaRepository: UserQuotaRepository,
    private readonly creditService: CreditService,
    private readonly licenseService: LicenseService,
    private readonly organizationQuotaRepository: OrganizationQuotaRepository,
    private readonly paymentService: PaymentService,
    private readonly bullhornIntegrationService: BullhornIntegrationService,
  ) {
    super(i18nService);
    sgMail.setApiKey(SendGridConfig.apiKey);
  }

  async hashPassword(password: string) {
    const salt = await bcrypt.genSalt();
    const hashPassword = await bcrypt.hash(password, salt);
    return {
      salt,
      hashPassword,
      password,
    };
  }

  async findOneById(userId: string, withDeleted?: boolean) {
    const user = await this.userRepository.findOne({
      where: { id: userId, ...(withDeleted ? {} : { isDeleted: false }) },
      withDeleted,
    });
    if (!user)
      throw new NotFoundException(
        await this.formatOutputData(
          { key: 'translate.USER_NOT_FOUND', lang: LanguageCode.United_States },
          { data: null, statusCode: StatusCode.USER_NOT_FOUND },
        ),
      );

    return user;
  }

  async findOneByIdWithCondition(
    conditions: FindOptionsWhere<UserEntity> | FindOptionsWhere<UserEntity>[],
    relations?: FindOptionsRelationByString | FindOptionsRelations<UserEntity>,
    options: { withDeleted?: boolean } = {
      withDeleted: false,
    },
  ) {
    const { withDeleted } = options;
    const user = await this.userRepository.findOne({
      where: conditions,
      relations,
      withDeleted,
    });
    if (!user) {
      throw new HttpException(
        await this.formatOutputData(
          {
            key: 'translate.USER_NOT_FOUND',
            lang: LanguageCode.United_States,
          },
          {
            data: null,
            statusCode: StatusCode.USER_NOT_FOUND,
          },
        ),
        HttpStatus.NOT_FOUND,
      );
    }

    return user;
  }

  async findOne(options: FindOneOptions<UserEntity>) {
    // Ensure role.rolePermissions is loaded
    if (options.relations) {
      // Convert relations to array if it's a string
      const relationsArray = Array.isArray(options.relations)
        ? options.relations
        : Object.keys(options.relations);

      if (relationsArray.includes('role.permissions')) {
        // Replace 'role.permissions' with 'role.rolePermissions' and 'role.rolePermissions.permission'
        const newRelations = [
          ...relationsArray.filter((rel: string) => rel !== 'role.permissions'),
          'role.rolePermissions',
          'role.rolePermissions.permission',
        ];

        // Update options.relations
        options.relations = newRelations;
      }
    }

    // Ensure isDeleted is false unless explicitly specified
    if (!options.where) {
      options.where = { isDeleted: false };
    } else if (
      typeof options.where === 'object' &&
      !Array.isArray(options.where) &&
      !('isDeleted' in options.where)
    ) {
      options.where = { ...options.where, isDeleted: false };
    }

    const user = await this.userRepository.findOne(options);

    // Create userPermissions from role.permissions for backward compatibility
    if (user && user.role && user.role.permissions) {
      user.userPermissions = user.role.permissions.map((permission) => {
        const userPermission = new UserPermissionEntity();
        userPermission.id = `${user.id}_${permission.id}`;
        userPermission.userId = user.id;
        userPermission.permissionId = permission.id;
        userPermission.allowRead = permission.allowRead;
        userPermission.allowWrite = permission.allowWrite;
        userPermission.permission = permission;
        return userPermission;
      });
    } else if (user && !user.userPermissions) {
      user.userPermissions = [];
    }

    return user;
  }

  async findUserDetailById(id: string) {
    const response = await this.userRepository.findOne({
      where: { id, isDeleted: false },
      relations: [
        'role',
        'role.rolePermissions',
        'role.rolePermissions.permission',
        'organization',
      ],
    });

    // Create userPermissions from role.permissions for backward compatibility
    if (response.role && response.role.permissions) {
      response.userPermissions = response.role.permissions.map((permission) => {
        const userPermission = new UserPermissionEntity();
        userPermission.id = `${response.id}_${permission.id}`;
        userPermission.userId = response.id;
        userPermission.permissionId = permission.id;
        userPermission.allowRead = permission.allowRead;
        userPermission.allowWrite = permission.allowWrite;
        userPermission.permission = permission;
        return userPermission;
      });
    } else {
      response.userPermissions = [];
    }

    return response;
  }

  async findUserByEmail(email: string) {
    const data = await this.findOne({
      where: { email, isDeleted: false },
      relations: { organization: true },
    });
    return this.formatOutputData({ key: 'FIND_USER_BY_EMAIL' }, { data });
  }

  async getAllPermissions() {
    const data = await this.dataSource.createQueryBuilder(PermissionEntity, 'p').getMany();

    return this.formatOutputData({ key: 'GET_ALL_PERMISSIONS' }, { data });
  }

  async getAllRoles() {
    const data = await this.dataSource.createQueryBuilder(RoleEntity, 'p').getMany();

    return this.formatOutputData({ key: 'GET_ALL_ROLES' }, { data });
  }

  async getAllOrganizations() {
    const organizations = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'organization')
      .leftJoinAndSelect('organization.users', 'user')
      .leftJoinAndSelect('user.role', 'role')
      .select([
        'organization.id',
        'organization.name',
        'user.id',
        'user.username',
        'user.fullName',
        'user.email',
        'user.avatarId',
        'role.name',
        'organization.bhClientId',
        'organization.bhClientSecret',
        'organization.bhUsername',
        'organization.bhPassword',
        'organization.bhToken',
        'organization.companySize',
        'organization.companyOwner',
        'organization.companyWebsite',
        'organization.companyIndustries',
        'organization.companyTypes',
        'organization.companyAvatar',
        'organization.address',
        'organization.phone',
        'organization.tags',
        'organization.license',
        'organization.companyEmail',
        'organization.companyAdmins',
        'organization.status',
        'organization.createdBy',
      ])
      // .where('organization.status = :status', { status: OrganizationStatusEnum.APPROVED })
      .orderBy('organization.status', 'DESC')
      .getMany();

    // Enhance organizations with credit information and user counts
    const enhancedOrganizations = await Promise.all(
      organizations.map(async (org) => {
        // Get zileo user count (active users in this organization)
        const zileoUserCount = await this.userRepository.count({
          where: { organizationId: org.id, isDeleted: false },
        });

        // Get only TOPUP organization credit information (exclude plan credits)
        const orgQuota = await this.organizationQuotaRepository.findOne({
          where: { organizationId: org.id, featureId: 'credits', source: QuotaSource.TOPUP },
        });

        const totalCredits = orgQuota?.originalAmount || 0;
        const usedCredits = totalCredits - (orgQuota?.remaining || 0);

        return {
          ...org,
          zileoUserCount,
          totalCredits,
          usedCredits,
        };
      }),
    );

    return this.formatOutputData({ key: 'GET_ALL_ORGANIZATIONS' }, { data: enhancedOrganizations });
  }

  async createOrganization(body: CreateOrganizationDto, user: IJwtPayload) {
    try {
      const existingOrganization = await this.dataSource
        .getRepository(OrganizationEntity)
        .findOne({ where: { name: body.name } });

      if (existingOrganization) {
        return await this.throwCommonMessage('CREATE_ORGANIZATION', 'ORGANIZATION EXISTING');
      }

      if (body?.companyAdmins?.length > 0) {
        const userEmailCount = await this.userRepository.find({
          where: { email: In(body.companyAdmins) },
        });
        if (userEmailCount.length > 0) {
          throw new BadRequestException('User already exists');
        }
      }

      const newOrganization = this.dataSource.getRepository(OrganizationEntity).create({
        name: body.name,
        bhClientId: body.bhClientId,
        bhClientSecret: body.bhClientSecret,
        bhUsername: body.bhUsername,
        bhPassword: body.bhPassword,
        companySize: body.companySize,
        companyAvatar: body.companyAvatar,
        companyIndustries:
          body.companyIndustries?.length > 0 ? body.companyIndustries?.join(',') : null,
        companyOwner: body.companyOwner,
        companyWebsite: body.companyWebsite,
        companyTypes: body.companyTypes?.length > 0 ? body.companyTypes?.join(',') : null,
        companyAdmins: body.companyAdmins?.length > 0 ? body.companyAdmins?.join(',') : null,
        address: body.address,
        phone: body.phone,
        license: body.license,
        companyEmail: body.companyEmail,
        tags: body.tags?.length > 0 ? body.tags?.join(',') : null,
      });

      const dataOrganization = await this.dataSource
        .getRepository(OrganizationEntity)
        .save(newOrganization);

      const adminRole = await this.roleRepository.findOne({ where: { keyCode: RoleEnum.ADMIN } });

      const dataSendInvite = body.companyAdmins?.map((email) => {
        return {
          email: email,
          roleId: adminRole.id,
          organizationId: dataOrganization.id,
          organizationName: dataOrganization.name,
        };
      });

      await this.inviteUser({ id: user.id }, { invitedUsers: dataSendInvite });

      return this.formatOutputData({ key: 'CREATE_ORGANIZATION_SUCCESS' }, { data: {} });
    } catch (err) {
      this.logger.error(err);
      console.error(err);
      return await this.throwCommonMessage('CREATE_ORGANIZATION', err.message || err);
    }
  }

  async getDetailOrganization(id: string) {
    try {
      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);
      const queriedOrganization = await organizationRepository.findOne({
        where: {
          id,
        },
        relations: {
          users: {
            role: true,
          },
        },
        select: {
          users: {
            id: true,
            email: true,
            username: true,
            fullName: true,
            password: true,
            role: {
              id: true,
              name: true,
              keyCode: true,
            },
            status: true,
            initialPassword: true,
          },
        },
      });

      const subscription = await this.subscriptionService.getSubscriptionForOrganization(id);

      if (!queriedOrganization) {
        return await this.throwCommonMessage('GET_DETAIL_ORGANIZATION', 'ORGANIZATION NOT FOUND');
      }

      // queriedOrganization.users = queriedOrganization.users.filter((item) => !item.isDeleted);
      const existingOrganization: any = { ...queriedOrganization, subscription };

      // Get only TOPUP organization credit information (exclude plan credits)
      const organizationCredit = await this.organizationQuotaRepository.findOne({
        where: { featureId: 'credits', organizationId: id, source: QuotaSource.TOPUP },
        select: ['remaining', 'originalAmount'],
      });

      // Add organization credit fields
      if (organizationCredit) {
        existingOrganization.organizationCredit = {
          remaining: organizationCredit.remaining || 0,
          originalAmount: organizationCredit.originalAmount || 0,
          totalCredits: organizationCredit.originalAmount || 0,
          usedCredits:
            (organizationCredit.originalAmount || 0) - (organizationCredit.remaining || 0),
          remainingCredits: organizationCredit.remaining || 0,
        };
      } else {
        existingOrganization.organizationCredit = {
          remaining: 0,
          originalAmount: 0,
          totalCredits: 0,
          usedCredits: 0,
          remainingCredits: 0,
        };
      }

      if (existingOrganization.users?.length) {
        const userQuotaRepository = this.dataSource.getRepository(QuotaUsageLogEntity);
        const userQuotas = await userQuotaRepository.find({
          where: {
            userId: In(existingOrganization.users.map((user) => user.id)),
            unit: FeatureUnitEnum.CREDIT,
          },
          select: ['userId', 'amount'],
        });

        const userQuotaMap = userQuotas.reduce(
          (map, quota) => {
            map[quota.userId] = quota.amount;
            return map;
          },
          {} as Record<string, number>,
        );

        for (const user of existingOrganization.users) {
          user.originalAmount = userQuotaMap[user.id] || 0;
        }
      }

      return this.formatOutputData(
        { key: 'GET_DETAIL_ORGANIZATION' },
        { data: existingOrganization },
      );
    } catch (err) {
      this.logger.error(err);
      console.error(err);
      return await this.throwCommonMessage('GET_DETAIL_ORGANIZATION', err.message || err);
    }
  }

  async updateOrganization(id: string, body: CreateOrganizationDto, user: IJwtPayload) {
    try {
      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);

      const existingOrganization = await organizationRepository.findOne({
        where: { name: body.name, id: Not(id) },
      });

      if (existingOrganization) {
        return await this.throwCommonMessage('UPDATE_ORGANIZATION', 'ORGANIZATION EXISTING');
      }

      await organizationRepository
        .createQueryBuilder()
        .update(OrganizationEntity)
        .set({
          name: body.name,
          bhClientId: body.bhClientId,
          bhClientSecret: body.bhClientSecret,
          bhUsername: body.bhUsername,
          bhPassword: body.bhPassword,
          companySize: body.companySize,
          companyAvatar: body.companyAvatar,
          companyIndustries:
            body.companyIndustries?.length > 0 ? body.companyIndustries?.join(',') : null,
          companyOwner: body.companyOwner,
          companyWebsite: body.companyWebsite,
          companyTypes: body.companyTypes?.length > 0 ? body.companyTypes?.join(',') : null,
          companyAdmins: body.companyAdmins?.length > 0 ? body.companyAdmins?.join(',') : null,
          address: body.address,
          phone: body.phone,
          license: body.license,
          companyEmail: body.companyEmail,
          tags: body.tags?.length > 0 ? body.tags?.join(',') : null,
          note: body.note,
          planType: body.planType,
        })
        .where('id = :id', { id })
        .execute();

      return this.formatOutputData({ key: 'UPDATE_ORGANIZATION_SUCCESS' }, { data: {} });
    } catch (err) {
      this.logger.error(err);
      console.error(err);
      return await this.throwCommonMessage('UPDATE_ORGANIZATION', err.message || err);
    }
  }

  async deleteOrganization(id: string) {
    try {
      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);

      const organizationToDelete = await organizationRepository.findOne({ where: { id: id } });

      if (!organizationToDelete) {
        return await this.throwCommonMessage('DELETE_ORGANIZATION', 'ORGANIZATION NOT FOUND');
      }

      await organizationRepository
        .createQueryBuilder()
        .softDelete()
        .from(OrganizationEntity)
        .where('id = :id', { id: id })
        .execute();

      await this.userRepository
        .createQueryBuilder()
        .update(UserEntity)
        .set({ isDeleted: true })
        .where('organizationId = :id', { id: id })
        .execute();

      return this.formatOutputData({ key: 'DELETE_ORGANIZATION_SUCCESS' }, { data: {} });
    } catch (err) {
      this.logger.error(err);
      console.error(err);
      return await this.throwCommonMessage('DELETE_ORGANIZATION', err.message || err);
    }
  }

  async getNumberOfLoginUser() {
    const result = await this.userRepository.countBy({
      expiredRefreshTokenDate: MoreThanOrEqual(new Date()),
    });

    return this.formatOutputData({ key: 'GET_NUMBER_OF_LOGIN_USER' }, { data: { count: result } });
  }

  async findById(id: string) {
    return this.findUserByConditions({ id });
  }

  async getMe(user: IJwtPayload) {
    const data = await this.findById(user.id);

    // Use features and permissions from JWT payload
    data.features = user.features;
    data.permissions = user.permissions;

    return this.formatOutputData({ key: 'GET_ME' }, { data });
  }

  async getMeViewAs(userId: string) {
    const data = await this.findById(userId);

    // Get permissions from database
    const permissions = await this.getUserPermissions(userId);
    // Calculate features based on permissions and role
    const features = calculateUserFeatures(data.role?.keyCode, permissions);

    data.features = features;
    data.permissions = permissions;

    return this.formatOutputData({ key: 'GET_ME' }, { data });
  }

  /**
   * Get user permissions from database
   */
  private async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get permissions from role_permissions table
      const result = await this.dataSource.query(
        `
        SELECT p."keyCode", rdp."allowRead", rdp."allowWrite"
        FROM users u
        JOIN roles r ON u."roleId" = r.id
        JOIN role_default_permissions rdp ON r.id = rdp."roleId"
        JOIN permissions p ON rdp."permissionId" = p.id
        WHERE u.id = $1
      `,
        [userId],
      );

      // Format permissions
      const permissions = [];
      for (const row of result) {
        if (row.allowRead) {
          permissions.push(`${row.keyCode}.Read`);
        }
        if (row.allowWrite) {
          permissions.push(`${row.keyCode}.Write`);
        }
      }

      return permissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  async getLeadValueById(
    id: string,
    filters: { country?: string; fromDate?: Date; toDate?: Date },
  ) {
    const { country, fromDate, toDate } = filters;

    const latestUser = await this.findById(id);

    if (!latestUser) {
      throw new Error('User not found');
    }
    const queryBuilder = this.dataSource
      .createQueryBuilder(JobLead, 'jl')
      .where('jl.dataKey = :dataKey', { dataKey: id });
    if (country) {
      queryBuilder.andWhere('jl.address_country = :country', { country });
    }
    if (fromDate) {
      queryBuilder.andWhere('jl.updated_at >= :fromDate', { fromDate });
    }
    if (toDate) {
      queryBuilder.andWhere('jl.updated_at <= :toDate', { toDate });
    }

    const [myLeadRange, myLeadRangeByMonth] = await Promise.all([
      queryBuilder
        .groupBy('jl.dataKey')
        .select(
          'sum(jl.minSalary) "totalMinSalary", sum(jl.maxSalary) "totalMaxSalary", sum(jl.salary) "totalSalary"',
        )
        .getRawOne<{ totalMinSalary: string; totalMaxSalary: string; totalSalary: string }>(),
      queryBuilder
        .clone()
        .select([
          `DATE_TRUNC('month', jl.updated_at) AS month`,
          `SUM(jl.minSalary) AS "totalMinSalary"`,
          `SUM(jl.maxSalary) AS "totalMaxSalary"`,
          `SUM(jl.salary) AS "totalSalary"`,
        ])
        .andWhere("jl.updated_at >= NOW() - INTERVAL '7 MONTHS'")
        .groupBy("DATE_TRUNC('month', jl.updated_at)")
        .orderBy("DATE_TRUNC('month', jl.updated_at)", 'ASC')
        .getRawMany<{
          month: string;
          totalMinSalary: string;
          totalMaxSalary: string;
          totalSalary: string;
        }>(),
    ]);

    const { totalMinSalary = 0, totalMaxSalary = 0, totalSalary = 0 } = myLeadRange || {};

    const minRange = Math.round(latestUser.potentialLeadValue * Number(totalMinSalary));
    const maxRange = Math.round(latestUser.potentialLeadValue * Number(totalMaxSalary));
    const leadValue = Math.round(latestUser.potentialLeadValue * Number(totalSalary));
    const totalData = { minRange, maxRange, leadValue };

    const monthData = myLeadRangeByMonth.map((item) => {
      const totalMinSalary = Number(item.totalMinSalary);
      const totalMaxSalary = Number(item.totalMaxSalary);
      const totalSalary = Number(item.totalSalary);

      const minRange = Math.round(latestUser.potentialLeadValue * totalMinSalary);
      const maxRange = Math.round(latestUser.potentialLeadValue * totalMaxSalary);
      const leadValue = Math.round(latestUser.potentialLeadValue * totalSalary);

      return {
        month: item.month,
        minRange,
        maxRange,
        leadValue,
      };
    });

    return {
      totalData: totalData,
      monthData: monthData,
    };
  }

  async getLeadValueUser(id: string, query: JobLeadStatsDto) {
    const data = await this.getLeadValueById(id, query);

    return this.formatOutputData({ key: 'GET_LEAD_VALUE_USER' }, { data });
  }

  async getMyLeadValueRange(userId: string, query: JobLeadStatsDto) {
    const data = await this.getLeadValueById(userId, query);

    return this.formatOutputData({ key: 'GET_MY_LEAD_VALUE_RANGE' }, { data });
  }

  async updateMe(user: IJwtPayload, updateMeDto: UpdateMeRequestDto) {
    try {
      await this.updateByConditions(
        { id: user.id },
        { ...this.userRepository.create(updateMeDto) },
      );

      return this.formatOutputData({ key: 'UPDATE_ME' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('UPDATE_ME', error);
    }
  }

  async updateMeViewAs(userId: string, updateMeDto: UpdateMeRequestDto) {
    try {
      await this.updateByConditions({ id: userId }, { ...this.userRepository.create(updateMeDto) });

      return this.formatOutputData({ key: 'UPDATE_ME' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('UPDATE_ME', error);
    }
  }

  async findUserByConditions(conditions: object) {
    const user = await this.userRepository.findOneBy({ ...conditions, isDeleted: false });
    if (!user) throw new NotFoundException();

    return user;
  }

  async checkValidCredential(loginDto: LoginDto) {
    const { emailOrUsername, password } = loginDto;
    let user: UserEntity;
    if (emailOrUsername.includes('@')) {
      user = await this.findOne({
        where: { email: loginDto.emailOrUsername, isDeleted: false, status: UserStatus.ACTIVE },
        select: ['email', 'password', 'id'],
      });
    } else {
      user = await this.findOne({
        where: { username: loginDto.emailOrUsername, isDeleted: false, status: UserStatus.ACTIVE },
        select: ['username', 'password', 'id'],
      });
    }

    if (!user) {
      throw new NotFoundException();
    }

    if (!user.password) {
      throw new BadGatewayException('You need to register with the system');
    }

    if (!bcrypt.compareSync(password, user.password)) {
      throw new UnauthorizedException();
    }

    return user;
  }

  async updateByConditions(
    conditions: FindOptionsWhere<UserEntity>,
    data: QueryDeepPartialEntity<UserEntity>,
    queryRunner?: QueryRunner,
  ) {
    if (queryRunner) {
      return queryRunner.manager.update(UserEntity, conditions, data);
    }

    return await this.userRepository.update(conditions, data);
  }

  async create(data: Partial<UserEntity>) {
    return this.userRepository.save(data);
  }

  async deleteBy(conditions: FindOptionsWhere<UserEntity>) {
    return this.userRepository.delete(conditions);
  }

  async getRolesMappingToId(): Promise<Record<string, RoleEntity>> {
    const roles = await this.roleRepository.find();
    const rolesMappingToId: Record<string, RoleEntity> = {};
    roles.forEach((item) => (rolesMappingToId[item.id] = item));

    return rolesMappingToId;
  }

  async updateConsultant(userId: string, body: UpdateSolutionsDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const userUpdate = await this.userRepository.update(user.id, {
        consultantId: body.consultant_id,
        consultantName: body.consultant_name,
      });

      return this.formatOutputData({ key: 'UPDATE_CONSULTANT' }, { data: userUpdate });
    } catch (error) {
      this.logger.error(error);
      console.error(error);
      return await this.throwCommonMessage('UPDATE_CONSULTANT', error);
    }
  }

  async inviteUser(senderUser: { id: string }, inviteUserDto: SendByEmailsDto) {
    try {
      throw new Error('Invite user was deprecated. Please use onboarding employee instead');

      return this.formatOutputData({ key: 'INVITE_USER' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      console.error(error);
      return await this.throwCommonMessage('INVITE_USER', error);
    }
  }

  /**
   * Add user to organization with license check and email invitation
   */
  async addUserToOrganization(addUserDto: any, currentUser: any) {
    try {
      const { email, username, organizationId, role } = addUserDto;

      // Determine target organization
      let targetOrgId = organizationId;
      if (!targetOrgId) {
        // If no organizationId provided, use current user's organization
        if (
          currentUser.role.keyCode !== RoleEnum.ADMIN &&
          currentUser.role.keyCode !== RoleEnum.SUPER_ADMIN
        ) {
          throw new BadRequestException('Only admins can add users to organizations');
        }
        targetOrgId = currentUser.organizationId;
      }

      // Validate organization exists
      const organization = await this.dataSource
        .getRepository(OrganizationEntity)
        .findOne({ where: { id: targetOrgId } });

      if (!organization) {
        throw new BadRequestException('Organization not found');
      }

      // Check if current user has permission to add users to this organization
      if (
        currentUser.role.keyCode === RoleEnum.ADMIN &&
        currentUser.organizationId !== targetOrgId
      ) {
        throw new BadRequestException('You can only add users to your own organization');
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findOne({ where: { email } });
      if (existingUser) {
        throw new BadRequestException('User with this email already exists');
      }

      // Validate role exists BEFORE other checks
      const roleData = await this.roleRepository.findOne({
        where: { keyCode: role || RoleEnum.BASIC_USER },
      });
      if (!roleData) {
        throw new BadRequestException('Invalid role specified');
      }

      // Check license availability BEFORE creating user
      const availableLicenseCount = await this.licenseService.getAvailableLicenseCount(targetOrgId);
      if (availableLicenseCount === 0) {
        throw new BadRequestException(
          'No available licenses for this organization. Please purchase more licenses or unassign existing ones.',
        );
      }

      // Get subscription and available licenses BEFORE creating user
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(targetOrgId);
      let availableLicenses = [];
      if (subscription) {
        availableLicenses = await this.licenseService.getAvailableLicenses(subscription.id);
        if (availableLicenses.length === 0) {
          throw new BadRequestException('No available licenses found for assignment');
        }
      } else if (currentUser.role.keyCode !== RoleEnum.SUPER_ADMIN) {
        throw new NotFoundException(
          'Cannot create user as there is no active subscription for this organization',
        );
      }

      // Check Bullhorn integration BEFORE creating user (if organization is connected)
      if (
        organization.license === LicenseType.CONNECTED &&
        organization.bhClientId &&
        organization.bhClientSecret
      ) {
        try {
          // Validate email exists in Bullhorn CorporateUser
          await this.validateEmailInBullhornCorporateUser(organization, email);
        } catch (error) {
          throw new BadRequestException(`Bullhorn integration error: ${error.message}`);
        }
      }

      // Generate temporary password
      const tempPassword = this.generateRandomPassword();
      const hashedPassword = await this.hashPassword(tempPassword);

      // Create user in transaction
      const newUser = await this.dataSource.transaction(async (entityManager) => {
        // Create user
        const userData: Partial<UserEntity> = {
          email,
          username: await this.generateUniqueUsername(username || email),
          password: hashedPassword.hashPassword,
          roleId: roleData.id,
          organizationId: targetOrgId,
          status: UserStatus.ACTIVE,
          initialPassword: tempPassword,
          licenseType: LicenseType.STANDARD,
        };

        const savedUser = await entityManager.save(UserEntity, userData);

        // Assign license if subscription exists (using pre-validated licenses)
        if (subscription && availableLicenses.length > 0) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[0].id,
            savedUser.id,
            currentUser.id,
            entityManager,
          );
        }

        return savedUser;
      });

      // Send invitation email
      await this.sendUserInvitationEmail(newUser, organization, tempPassword);

      return this.formatOutputData(
        { key: 'ADD_USER_SUCCESS' },
        {
          data: {
            id: newUser.id,
            email: newUser.email,
            fullName: newUser.fullName,
            username: newUser.username,
            status: newUser.status,
            organizationId: newUser.organizationId,
          },
        },
      );
    } catch (error) {
      this.logger.error('Error in addUserToOrganization:', error);
      return this.throwCommonMessage('ADD_USER_TO_ORGANIZATION', error);
    }
  }

  /**
   * Generate unique username from email
   */
  private async generateUniqueUsername(email: string): Promise<string> {
    const baseUsername = email.split('@')[0];
    let username = baseUsername;
    let counter = 0;

    // Check if username already exists in the organization
    while (await this.isUsernameExists(username)) {
      counter++;
      // Add random suffix to make it unique
      const randomSuffix = Math.floor(Math.random() * 1000)
        .toString()
        .padStart(3, '0');
      username = `${baseUsername}${counter}${randomSuffix}`;
    }

    return username;
  }

  /**
   * Check if username exists in organization
   */
  private async isUsernameExists(username: string): Promise<boolean> {
    const existingUser = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where('LOWER(username) = :username AND is_deleted = false', { username })
      .select('LOWER(username) AS username')
      .getRawOne();

    return !!existingUser;
  }

  /**
   * Generate random password for new users
   */
  private generateRandomPassword(): string {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  /**
   * Send invitation email to new user
   */
  private async sendUserInvitationEmail(
    user: UserEntity,
    organization: OrganizationEntity,
    tempPassword: string,
  ) {
    try {
      const emailContent = `
        <h2>Welcome to ${organization.name}!</h2>
        <p>You have been invited to join ${organization.name} on our platform.</p>
        <p><strong>Your login credentials:</strong></p>
        <ul>
          <li>Email: ${user.email}</li>
          <li>Temporary Password: ${tempPassword}</li>
        </ul>
        <p>Please log in and change your password immediately.</p>
        <p><a href="${APP_CONFIG.CLIENT_URL}/login">Login Here</a></p>
      `;

      const msg = {
        to: user.email,
        from: {
          name: 'Zileo',
          email: '<EMAIL>',
        },
        subject: `Welcome to ${organization.name}`,
        html: emailContent,
      };

      await sgMail.send(msg).catch((warn) => {
        console.warn('Error in sending email', warn?.response?.body?.errors);
      });

      this.logger.log(`Invitation email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(`Failed to send invitation email to ${user.email}:`, error);
      throw new Error('Failed to send invitation email');
    }
  }

  /**
   * Get organization licenses information
   */
  async getOrganizationLicenses(organizationId: string, currentUser: any) {
    try {
      // Check permissions
      if (
        !(
          currentUser.role.keyCode === RoleEnum.SUPER_ADMIN ||
          (currentUser.role.keyCode === RoleEnum.ADMIN &&
            currentUser.organizationId === organizationId)
        )
      ) {
        throw new BadRequestException('You can only view licenses for your own organization');
      }

      // Validate organization exists
      const organization = await this.dataSource
        .getRepository(OrganizationEntity)
        .findOne({ where: { id: organizationId } });

      if (!organization) {
        throw new BadRequestException('Organization not found');
      }

      // Get license information
      const licenseSummary =
        await this.licenseService.getLicenseSummaryForOrganization(organizationId);
      const totalLicenses = licenseSummary.totalLicenses;
      const availableLicenses = licenseSummary.availableLicenses;
      const usedLicenses = licenseSummary.assignedLicenses;

      // Get subscription info
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

      let users = [];

      // If connected organization, get users from Bullhorn
      if (
        organization.license === LicenseType.CONNECTED &&
        organization.bhClientId &&
        organization.bhClientSecret
      ) {
        try {
          const bullhornUsers = await this.getBullhornCorporateUsers(organization);

          // Get existing user emails from local database
          const existingUsers = await this.userRepository.find({
            // where: { organizationId: organization.id },
            select: ['email'],
          });
          const existingEmails = new Set(
            existingUsers.map((user) => user.email?.toLowerCase()).filter(Boolean),
          );

          // Filter out users that already exist in local database
          users = bullhornUsers.filter(
            (user) => user.email && !existingEmails.has(user.email.toLowerCase()),
          );
        } catch (error) {
          this.logger.warn(`Failed to get Bullhorn users: ${error.message}`);
        }
      }

      return this.formatOutputData(
        { key: 'GET_ORGANIZATION_LICENSES_SUCCESS' },
        {
          data: {
            organizationId,
            organizationName: organization.name,
            licenseType: organization.license,
            isConnected: organization.license === LicenseType.CONNECTED,
            licenses: {
              total: totalLicenses,
              used: usedLicenses,
              available: availableLicenses,
            },
            subscription: subscription
              ? {
                  id: subscription.id,
                  status: subscription.status,
                  planId: subscription.planId,
                  licenseCount: subscription.licenseCount,
                }
              : null,
            bullhornUsers: users,
          },
        },
      );
    } catch (error) {
      this.logger.error('Error in getOrganizationLicenses:', error);
      return this.throwCommonMessage('GET_ORGANIZATION_LICENSES', error);
    }
  }

  /**
   * Validate email exists in Bullhorn CorporateUser
   */
  private async validateEmailInBullhornCorporateUser(
    organization: OrganizationEntity,
    email: string,
  ) {
    try {
      // Search for CorporateUser with specific email
      const result = await this.bullhornIntegrationService.searchCorporateUsersWithPagination(
        organization.id,
        {
          query: `email:"${email}"`,
          fields: 'id,firstName,lastName,email,username,isDeleted',
          count: '1', // Only need to check if exists
        },
      );

      const corporateUsers = result || [];

      if (corporateUsers.length === 0) {
        throw new BadRequestException(
          `Email ${email} not found in Bullhorn CorporateUser. Please ensure the user exists in your Bullhorn system.`,
        );
      }

      const corporateUser = corporateUsers[0];
      if (corporateUser.isDeleted) {
        throw new BadRequestException(`User with email ${email} is deleted in Bullhorn system.`);
      }

      return corporateUser;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error validating email in Bullhorn CorporateUser: ${error.message}`);
      throw new BadRequestException(
        `Failed to validate email in Bullhorn system: ${error.message}`,
      );
    }
  }

  /**
   * Get users from Bullhorn CorporateUser with hasMore pagination
   */
  private async getBullhornCorporateUsers(organization: OrganizationEntity) {
    try {
      // Use the new searchCorporateUsersWithPagination method with hasMore logic
      const corporateUsers =
        await this.bullhornIntegrationService.searchCorporateUsersWithPagination(organization.id, {
          query: '',
          fields: 'id,firstName,lastName,email,username,isDeleted,dateAdded',
          // Don't set count to get all users
        });

      return corporateUsers.map((user) => ({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        source: 'bullhorn',
        status: user.isDeleted ? 'inactive' : 'active',
        dateAdded: user.dateAdded,
      }));
    } catch (error) {
      this.logger.error(`Error getting Bullhorn CorporateUsers: ${error.message}`);
      return [];
    }
  }

  async getStandardUserByInviting(
    item: SendByEmailItemDto,
    sender: UserEntity,
    rolesMappingToId: Record<string, RoleEntity>,
  ) {
    const { organizationId, organizationName, roleId } = item;
    const invitingRole = rolesMappingToId[roleId]?.keyCode;

    if (!invitingRole) {
      throw new BadRequestException('Invalid Role');
    }

    if (invitingRole === RoleEnum.ADMIN) {
      if (organizationId) {
        return item;
      }

      if (organizationName) {
        const organization = await this.dataSource
          .createQueryBuilder(OrganizationEntity, 'o')
          .where({ name: ILike(organizationName) })
          .getOne();

        if (organization) {
          return { ...item, organizationId: organization.id };
        }

        const createdOrganization = await this.dataSource
          .createQueryBuilder()
          .insert()
          .into(OrganizationEntity)
          .values({ name: organizationName })
          .execute();
        return {
          ...item,
          organizationId: createdOrganization.identifiers?.[0]?.id,
        };
      }

      throw new BadRequestException('This user should be assigned to an company');
    } else if (invitingRole === RoleEnum.BASIC_USER) {
      if (sender.role.keyCode === RoleEnum.SUPER_ADMIN) {
        throw new BadGatewayException(
          'Super Admin should create a company admin to invite their own staff',
        );
      }

      return { ...item, organizationId: sender.organizationId };
    }
  }

  async getUsers(rawCondition?: FindOptionsWhere<UserEntity>[] | FindOptionsWhere<UserEntity>) {
    const condition = rawCondition ?? { isDeleted: false };

    return this.userRepository.find({
      select: ['id', 'email', 'organization', 'role', 'username', 'consultantId', 'consultantName'],
      where: condition,
      relations: {
        organization: true,
        role: true,
      },
    });
  }

  async getCountUsers(
    rawCondition?: FindOptionsWhere<UserEntity>[] | FindOptionsWhere<UserEntity>,
  ) {
    const condition = rawCondition ?? { isDeleted: false };

    return this.userRepository.count({
      where: condition,
    });
  }

  async getMyUsers(userId: string, query: UserSearchDto) {
    const user = await this.userRepository.findOne({
      where: { id: userId, isDeleted: false },
      relations: { role: true },
    });

    if (!user) {
      return [];
    }

    const { role, organizationId } = user;
    const { organizationId: queryOrganizationId, searchText } = query;

    const qb = this.dataSource
      .createQueryBuilder(UserEntity, 'user')
      .select([
        'user.id',
        'user.email',
        'user.username',
        'user.consultantId',
        'user.consultantName',
        'user.potentialLeadValue',
        'user.fullName',
        'user.lastActivity',
        'user.avatarId',
        'user.status',
      ])
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('(user.status = :activeStatus OR user.status = :pendingStatus)', {
        activeStatus: UserStatus.ACTIVE,
        pendingStatus: UserStatus.PENDING,
      });

    if (searchText) {
      const wildCardSearchText = `%${searchText.toLowerCase()}%`;
      qb.andWhere(
        `(user.username ILIKE :searchText
        OR user.fullName ILIKE :searchText
        OR user.email ILIKE :searchText
        OR role.name ILIKE :searchText)`,
        { searchText: wildCardSearchText },
      );
    }

    if (role.keyCode === RoleEnum.SUPER_ADMIN || role.keyCode === RoleEnum.SALES) {
      if (queryOrganizationId) {
        qb.andWhere('user.organizationId = :queryOrganizationId', {
          queryOrganizationId,
        });
      }
    } else if (role.keyCode === RoleEnum.COMPANY_ADMIN || role.keyCode === RoleEnum.ADMIN) {
      qb.andWhere('user.organizationId = :organizationId', { organizationId });
    } else {
      return [user];
    }

    qb.andWhere('user.isDeleted = :isDeleted', { isDeleted: false });

    const users = await qb.getMany();

    return users;
  }

  async getUserMetrics(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId, isDeleted: false },
      relations: { role: true },
    });

    let usersCount = 0;

    if (!user) {
      return {
        usersCount,
        pendingUserCount: 0,
        organizations: [],
        organizationsCount: 0,
        usersCountByRole: {},
      };
    }

    const { role, organizationId } = user;
    const roleBasedCondition: any = {};
    const organizationBasedCondition: any = {
      status: OrganizationStatusEnum.APPROVED,
    };
    if (![RoleEnum.SUPER_ADMIN, RoleEnum.SALES].includes(role.keyCode)) {
      roleBasedCondition.organizationId = organizationId;
      organizationBasedCondition.id = organizationId;
    }
    usersCount = await this.getCountUsers({ ...roleBasedCondition, isDeleted: false });
    const pendingUserCount = await this.getCountUsers({
      ...roleBasedCondition,
      isDeleted: false,
      status: UserStatus.PENDING,
    });

    const organizations = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .select(['o.id', 'o.name'])
      .where(organizationBasedCondition)
      .getMany();

    const roleCountQuery = this.dataSource
      .createQueryBuilder(UserEntity, 'user')
      .leftJoin('user.role', 'role')
      .select('role.keyCode', 'roleKeyCode')
      .addSelect('role.name', 'roleName')
      .addSelect('COUNT(user.id)', 'count')
      .where('user.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere(roleBasedCondition)
      .groupBy('role.keyCode')
      .addGroupBy('role.name')
      .orderBy('count', 'DESC');

    const result = await roleCountQuery.getRawMany();

    return {
      usersCount,
      pendingUserCount,
      organizations,
      organizationsCount: organizations.length,
      usersCountByRole: result.reduce((acc, item) => {
        acc[item.roleKeyCode] = item.count;
        return acc;
      }, {}),
    };
  }

  async getLinkedInMetrics(userId: string, filter: any) {
    const { fromDate, toDate, action } = filter;
    const whereConditions = [];

    if (userId) {
      whereConditions.push(['(o.user_id = :userId)', { userId }]);
    }

    if (action) {
      whereConditions.push(['(o.action = :action)', { action }]);
    }

    if (fromDate) {
      whereConditions.push(['(o.created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(o.created_at <= :toDate)', { toDate }]);
    }
    const organizationRepository = this.dataSource.getRepository(SequenceStepTaskEntity);
    const queryBuilder = organizationRepository
      .createQueryBuilder('o')
      .select('COUNT(id)', 'count');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    const totalData = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .clone()
      .select([`DATE_TRUNC('month', o.created_at) as month`, 'COUNT(id) as count'])
      .groupBy(`DATE_TRUNC('month', o.created_at)`)
      .orderBy(`DATE_TRUNC('month', o.created_at)`, 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));

    return this.formatOutputData(
      { key: 'GET_LINKEDIN_METRIC' },
      {
        data: {
          totalCount: totalData.count,
          monthData: dataByMonth,
        },
      },
    );
  }

  async getSequenceMetrics(userId: string, query: JobLeadStatsDto) {
    const { country, fromDate, toDate } = query;

    const user = await this.userRepository.findOne({
      where: { id: userId, isDeleted: false },
      relations: { role: true },
    });

    if (!user) {
      return [];
    }

    const SAVED_HOUR_PER_STEP = 3;
    const metricsWhereConditions = [];
    const actionWhereConditions = [];
    const activityLogWhereConditions = [];
    const jobLeadWhereConditions = [];

    // Add dynamic search conditions to metrics
    if (country) {
      metricsWhereConditions.push(['sequence.country = :country', { country }]);
      actionWhereConditions.push(['sequence.country = :country', { country }]);
      activityLogWhereConditions.push(['sequence.country = :country', { country }]);
      jobLeadWhereConditions.push(['jl.address_country = :country', { country }]);
    }
    if (fromDate) {
      metricsWhereConditions.push(['sequence.created_at >= :fromDate', { fromDate }]);
      actionWhereConditions.push(['sequence_instances.created_at >= :fromDate', { fromDate }]);
      activityLogWhereConditions.push([
        'sequence_activity_log.created_at >= :fromDate',
        { fromDate },
      ]);
      jobLeadWhereConditions.push(['jl.updated_at >= :fromDate', { fromDate }]);
    }
    if (toDate) {
      metricsWhereConditions.push(['sequence.created_at <= :toDate', { toDate }]);
      actionWhereConditions.push(['sequence_instances.created_at <= :toDate', { toDate }]);
      activityLogWhereConditions.push(['sequence_activity_log.created_at <= :toDate', { toDate }]);
      jobLeadWhereConditions.push(['jl.updated_at <= :toDate', { toDate }]);
    }

    // Apply filters to the first query (metrics)
    const metricsQueryBuilder = this.dataSource
      .createQueryBuilder(SequenceEntity, 'sequence')
      .select([
        'COUNT(sequence.id) as count_sequence',
        'SUM(sequence.replied_count) as total_replied',
        'SUM(sequence.opened) as total_opened',
        'SUM(sequence.sent_count) as total_sent',
      ])
      .where('sequence.user_id = :userId', { userId });

    metricsWhereConditions.forEach(([condition, params]) => {
      metricsQueryBuilder.andWhere(condition, params);
    });

    const actionQueryBuilder = this.dataSource
      .createQueryBuilder(SequenceInstanceEntity, 'sequence_instances')
      .innerJoin('sequence_instances.sequence', 'sequence')
      .where('sequence_instances.user_id = :userId', { userId })
      .andWhere('sequence_instances.status = :status', { status: SequenceStepStatus.SENT });

    actionWhereConditions.forEach(([condition, params]) => {
      actionQueryBuilder.andWhere(condition, params);
    });

    const sequenceEngagedContactsQueryBuilder = this.sequenceActivityLogRepository
      .createQueryBuilder('sequence_activity_log')
      .innerJoin('sequence_activity_log.sequence', 'sequence')
      .where('sequence.user_id = :userId', { userId })
      .andWhere('sequence_activity_log.type IN (:...types)', {
        types: [
          SequenceActivityType.REPLIED,
          SequenceActivityType.LINK_CLICKED,
          SequenceActivityType.OPENED,
        ],
      })
      .andWhere(
        "jsonb_extract_path_text(sequence_activity_log.content, 'contact', 'email') IS NOT NULL",
      )
      .andWhere(
        "jsonb_extract_path_text(sequence_activity_log.content, 'contact', 'name') IS NOT NULL",
      )
      .select(
        "DISTINCT jsonb_extract_path_text(sequence_activity_log.content, 'contact', 'email') AS email, jsonb_extract_path_text(sequence_activity_log.content, 'contact', 'name') AS name, jsonb_extract_path_text(sequence_activity_log.content, 'contact', 'address') AS address",
      );

    activityLogWhereConditions.forEach(([condition, params]) => {
      sequenceEngagedContactsQueryBuilder.andWhere(condition, params);
    });

    const jobLeadQueryBuilder = this.dataSource
      .createQueryBuilder(SequenceStepTaskEntity, 'st')
      .where('st.user_id = :userId', { userId })
      .andWhere('st.type IN (:...types)', {
        types: [SequenceStepType.EMAIL, SequenceStepType.LINKEDIN_CONNECTION_REQUEST],
      });

    jobLeadWhereConditions.forEach(([condition, params]) => {
      jobLeadQueryBuilder.andWhere(condition, params);
    });

    const [metrics, sequencedAction, sequenceEngagedContacts, jobLeadsCount] = await Promise.all([
      metricsQueryBuilder.getRawOne(),
      actionQueryBuilder.getCount(),
      sequenceEngagedContactsQueryBuilder.getRawMany(),
      jobLeadQueryBuilder.getCount(),
    ]);

    const data = {
      sequencedAction,
      timeSaved: Number(jobLeadsCount) * SAVED_HOUR_PER_STEP,
      openRate:
        Number(((Number(metrics.total_opened) / Number(metrics.total_sent)) * 100).toFixed(2)) || 0,
      engagedContacts: sequenceEngagedContacts,
    };

    return this.formatOutputData({ key: 'GET_SEQUENCE_METRICS' }, { data: { data } });
  }

  async deleteUserById(userId: string, updatedBy: string) {
    return this.userRepository.update(
      { id: userId },
      {
        updatedBy,
        isDeleted: true,
      },
    );
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto, role: string) {
    const { ...restUpdatedDto } = updateUserDto;
    try {
      const user = await this.userRepository.findOneBy({ id, isDeleted: false });

      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      if (updateUserDto.organizationId) {
        const organization = await this.dataSource
          .createQueryBuilder(OrganizationEntity, 'o')
          .where({ id: updateUserDto.organizationId })
          .getOne();

        if (!organization) {
          throw new BadRequestException('Missing organization');
        }

        if (role !== RoleEnum.SUPER_ADMIN) {
          throw new BadRequestException("You Don't have permission");
        }
      }

      if (updateUserDto.organization && !updateUserDto.organizationId) {
        const organizationByName = await this.dataSource
          .createQueryBuilder(OrganizationEntity, 'o')
          .where({ name: updateUserDto.organization })
          .getOne();

        if (organizationByName) {
          updateUserDto.organizationId = organizationByName.id;
        } else {
          const newOrganization = new OrganizationEntity();
          newOrganization.name = updateUserDto.organization;
          const saveOriginal = await this.dataSource
            .createQueryBuilder(OrganizationEntity, 'o')
            .insert()
            .into(OrganizationEntity)
            .values(newOrganization)
            .execute();

          updateUserDto.organizationId = saveOriginal.identifiers[0].id;
        }
      }

      const dataSaveUser = {
        roleId: updateUserDto?.roleId,
        email: updateUserDto?.email,
        fullName: updateUserDto?.fullName,
        username: updateUserDto?.username,
        organizationId: updateUserDto.organizationId,
        avatarId: updateUserDto?.avatarId,
        jobTitle: updateUserDto?.jobTitle,
        linkedinUrl: updateUserDto?.linkedinUrl,
        timezone: updateUserDto?.timezone,
        disableAccount: updateUserDto?.disableAccount,
      };

      if (restUpdatedDto) {
        await this.userRepository.update(id, this.userRepository.create(dataSaveUser));
      }

      return this.formatOutputData({ key: 'UPDATE_USER' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('UPDATE_USER', error);
    }
  }

  async getRolePermissions(roleId: string) {
    // Get role with permissions
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });

    if (!role || !role.permissions) {
      return [];
    }

    // Format role permissions
    const permissions = role.permissions.flatMap((permission: any) => {
      const result = [];
      if (permission.allowRead) {
        result.push(`${permission.keyCode}.Read`);
      }
      if (permission.allowWrite) {
        result.push(`${permission.keyCode}.Write`);
      }
      return result;
    });

    return permissions;
  }

  async updateBullhornConfig(userId: string, bodyDto: UpdateBullhornConfigDto, req: any) {
    try {
      const { clientId, clientSecret, username, password } = bodyDto;
      const user = await this.userRepository.findOne({
        where: { id: userId, isDeleted: false },
        select: { organizationId: true, role: { keyCode: true }, id: true },
        relations: { role: true },
      });

      if (!user) {
        return this.throwCommonMessage(
          'UPDATE_BULLHORN_CONFIG_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }

      const {
        organizationId,
        role: { keyCode },
      } = user;
      if (keyCode !== RoleEnum.SUPER_ADMIN && keyCode !== RoleEnum.ADMIN) {
        return this.throwCommonMessage(
          'UPDATE_BULLHORN_CONFIG_USER_UNAUTHORIZED',
          new BadRequestException('Only Company Admin could edit ApiKey'),
        );
      }

      if (keyCode === RoleEnum.ADMIN && !organizationId) {
        return this.throwCommonMessage(
          'UPDATE_BULLHORN_CONFIG_USER_UNAUTHORIZED',
          new BadRequestException(
            'You are not assigned to any company yet. Please contact Super Admin to get support',
          ),
        );
      }

      if (!organizationId) {
        return this.throwCommonMessage(
          'UPDATE_BULLHORN_CONFIG_USER_FAIL',
          new BadRequestException(
            'You are not assigned to a company yet. Please contact Admin to support',
          ),
        );
      }

      await this.dataSource
        .createQueryBuilder(OrganizationEntity, 'o')
        .update()
        .where({ id: organizationId })
        .set({
          bhClientId: clientId,
          bhClientSecret: clientSecret,
          bhUsername: username,
          bhPassword: password,
        })
        .execute();

      delete req.app.locals?.[organizationId];

      return this.formatOutputData({ key: 'UPDATE_BULLHORN_CONFIG_SUCCESS' }, { data: {} });
    } catch (error) {
      console.log('Error in updateBullhornConfig', error);
      return this.throwCommonMessage(
        'UPDATE_BULLHORN_CONFIG_SOMETHING_WENT_WRONG',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async getBullhornConfig(userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, isDeleted: false },
        // select: { organizationId: true, role: { keyCode: true }, id: true },
        relations: { organization: true },
      });

      if (!user) {
        return this.throwCommonMessage(
          'UPDATE_BULLHORN_CONFIG_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }

      return this.formatOutputData({ key: 'GET_BULLHORN_CONFIG_SUCCESS' }, { data: { user } });
    } catch (error) {
      console.log('Error in getBullhornConfig', error);
      return this.throwCommonMessage(
        'UPDATE_BULLHORN_CONFIG_SOMETHING_WENT_WRONG',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async updateUserPassword(userId: string, bodyDto: UpdatePasswordDto) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, isDeleted: false },
      });

      if (!user) {
        return this.throwCommonMessage(
          'UPDATE_PASSWORD_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }

      const salt = await bcrypt.genSalt();
      const hashPassword = await bcrypt.hash(bodyDto.password, salt);
      await this.userRepository.update(user.id, { password: hashPassword });

      return this.formatOutputData({ key: 'UPDATE_PASSWORD_SUCCESS' }, { data: {} });
    } catch (error) {
      console.log('Error in updateBullhornConfig', error);
      return this.throwCommonMessage(
        'UPDATE_PASSWORD_SOMETHING_WENT_WRONG',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async uploadAvatar(avatar: Express.Multer.File) {
    try {
      const { originalname, buffer } = avatar;
      const extension = originalname.split('.').pop();
      const imgId = await this.fileUploadService.uploadPublicFile(
        buffer,
        `avatars/${uuid()}.${extension}`,
      );
      const url = await this.fileUploadService.getSignedViewUrl(imgId);
      const data = {
        url,
        imgId,
      };
      return this.formatOutputData({ key: 'UPLOAD_AVATAR_SUCCESS' }, { data: { data } });
    } catch (error) {
      return this.throwCommonMessage(
        'UPLOAD_AVATAR_SOMETHING_WENT_WRONG',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async uploadFile(file: Express.Multer.File) {
    try {
      const { originalname, buffer } = file;
      const fileId = await this.fileUploadService.uploadPublicFile(
        buffer,
        `attachments/${uuid()}_${originalname}`,
      );
      const url = await this.fileUploadService.getSignedViewUrl(fileId, false);
      const data = {
        url,
        fileId,
      };
      return this.formatOutputData({ key: 'UPLOAD_FILE_SUCCESS' }, { data: { data } });
    } catch (error) {
      return this.throwCommonMessage(
        'UPLOAD_FILE_SOMETHING_WENT_WRONG',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async linkedInConnection(body: LinkedConnectionDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, isDeleted: false },
      });

      if (!user) {
        return this.throwCommonMessage(
          'LINKED_CONNECTION_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }

      const {
        username,
        password,
        accessToken,
        premiumToken,
        country,
        ip,
        userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      } = body;
      const payload: any = {
        ...(accessToken
          ? { access_token: accessToken, premium_token: premiumToken }
          : { username, password }),
        user_agent: userAgent,
        provider: unipileConfig.UNIPILE_PROVIDER,
      };
      if (country) {
        payload.country = country;
      }
      if (ip) {
        payload.ip = ip;
      }

      const data = await unipileClient.connectAccount(payload);
      if (data.object === 'Checkpoint') {
        // Request for checkpoint
        return this.formatOutputData(
          { key: 'LINKEDIN_CONNECTION_CHECKPOINT' },
          { data: { data: { ...data, provider: payload.provider } } },
        );
      }
      const unipileAccountId = data.account_id;

      await this.userRepository.update(user.id, {
        unipileAccountId,
        unipileAccountStatus: UnipileAccountStatus.CONNECTED,
        linkedInCountryCode: payload.country,
        linkedInIP: payload.ip,
      });

      return this.formatOutputData({ key: 'LINKEDIN_CONNECTION_SUCCESS' }, { data: { data } });
    } catch (e) {
      console.error('Error in linkedInConnection: ', e);
      return this.throwCommonMessage(
        'LINKEDIN_CONNECTION',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async resolveUnipileCheckpoint(payload: UnipileSolveCheckpointDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        return this.throwCommonMessage(
          'LINKED_CONNECTION_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }
      const data = await unipileClient.resolveCheckpoint(payload);
      if (data.object === 'Checkpoint') {
        // Request for checkpoint
        return this.formatOutputData(
          { key: 'LINKEDIN_CONNECTION_CHECKPOINT' },
          { data: { data: { ...data, provider: payload.provider } } },
        );
      }
      const unipileAccountId = data.account_id;
      if (user.unipileAccountId) {
        try {
          await unipileClient.deleteAccount(user.unipileAccountId);
        } catch (e) {
          console.error('Error in deleting unipile account: ', e);
        }
      }
      await this.userRepository.update(user.id, {
        unipileAccountId,
        unipileAccountStatus: UnipileAccountStatus.CONNECTED,
      });

      return this.formatOutputData(
        { key: 'UNIPILE_RESOLVE_CHECKPOINT_SUCCESS' },
        { data: { data } },
      );
    } catch (error) {
      console.error('Error in resolveUnipileCheckpoint: ', error);
      return this.throwCommonMessage(
        'UNIPILE_RESOLVE_CHECKPOINT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async resendUnipileCheckpoint(payload: UnipileResendCheckpointDto) {
    try {
      const data = await unipileClient.resendCheckpoint(payload);
      return this.formatOutputData(
        { key: 'UNIPILE_RESEND_CHECKPOINT_SUCCESS' },
        { data: { data: { ...data, provider: payload.provider } } },
      );
    } catch (error) {
      console.error('Error in resendUnipileCheckpoint: ', error);
      return this.throwCommonMessage(
        'UNIPILE_RESEND_CHECKPOINT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async getUnipileAccount(userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user || !user.unipileAccountId) {
        return this.throwCommonMessage(
          'UNIPILE_ACCOUNT_NOT_FOUND',
          new NotFoundException('Unipile account not found'),
        );
      }

      const defaultProfileStatus = 'OK';

      const data = await unipileClient.retrieveAccount(user.unipileAccountId);
      let dataLinkedInProfile = null;
      if (data?.sources[0]?.status != defaultProfileStatus) {
        await this.userRepository.update(user.id, {
          unipileAccountStatus: UnipileAccountStatus.DISCONNECTED,
        });
      } else {
        dataLinkedInProfile = await unipileClient.getProfile(user.unipileAccountId, 'me');
        await this.userRepository.update(user.id, {
          linkedinAvatarUrl: dataLinkedInProfile?.profile_picture_url,
          linkedinPublicIdentifier: dataLinkedInProfile?.public_identifier,
        });
      }

      const currentStatus = user?.unipileAccountStatus;
      const latestStatus =
        data?.sources[0]?.status !== defaultProfileStatus
          ? UnipileAccountStatus.DISCONNECTED
          : UnipileAccountStatus.CONNECTED;

      if (currentStatus !== latestStatus) {
        await this.userRepository.update(user.id, {
          unipileAccountStatus: latestStatus,
        });
      }

      return this.formatOutputData(
        { key: 'GET_UNIPILE_ACCOUNT_STATUS' },
        {
          data: {
            data: { ...data, dataLinkedInProfile, linkedInCountryCode: user.linkedInCountryCode },
          },
        },
      );
    } catch (error) {
      console.error('Error in getUnipileAccountStatus: ', error);
      return this.throwCommonMessage(
        'GET_UNIPILE_ACCOUNT_STATUS',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async retrieveUnipileAccount(accountId: string) {
    try {
      const data = await unipileClient.retrieveAccount(accountId);
      return this.formatOutputData(
        { key: 'UNIPILE_RETRIEVE_UNIPILE_ACCOUNT_SUCCESS' },
        { data: { data } },
      );
    } catch (error) {
      console.error('Error in retrieveUnipileAccount: ', error);
      return this.throwCommonMessage(
        'UNIPILE_RESEND_CHECKPOINT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async updateUniplileAccountInApp(userId: string, body: UnipileUpdateAccountInApp) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        return this.throwCommonMessage(
          'LINKED_CONNECTION_USER_NOT_FOUND',
          new NotFoundException('User not found'),
        );
      }

      await this.userRepository.update(user.id, {
        unipileAccountId: body.account_id,
        unipileAccountStatus: UnipileAccountStatus.CONNECTED,
      });
      return this.formatOutputData({ key: 'UPDATE_UNIPILE_ACCOUNT_IN_APP_SUCCESS' }, { data: {} });
    } catch (error) {
      console.error('Error in retrieveAccount: ', error);
      return this.throwCommonMessage(
        'UPDATE_UNIPILE_ACCOUNT_IN_APP',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async unLinkedInConnection(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      return this.throwCommonMessage(
        'LINKED_CONNECTION_USER_NOT_FOUND',
        new NotFoundException('User not found'),
      );
    }
    try {
      await unipileClient.deleteAccount(user.unipileAccountId);
      await this.userRepository.update(user.id, {
        unipileAccountId: null,
        unipileAccountStatus: null,
      });

      return this.formatOutputData({ key: 'UN_LINKEDIN_CONNECTION_SUCCESS' }, { data: {} });
    } catch (e) {
      if (e?.response?.data?.type === 'errors/resource_not_found') {
        return await this.userRepository.update(user.id, {
          unipileAccountId: null,
          unipileAccountStatus: null,
        });
      }
      return this.throwCommonMessage(
        'UN_LINKEDIN',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async unipileWebhook(data: any) {
    try {
      const unipileAccountId = data.account_id || data.AccountStatus?.account_id || 'UNKNOWN';
      const unipileAccountType = data.account_type || data.AccountStatus?.account_type || 'UNKNOWN';
      console.log(
        `[UNIPILE] @AccountID: ${unipileAccountId}, @AccountType: ${unipileAccountType}, @Webhook:`,
        JSON.stringify(data),
      );

      if (
        data.AccountStatus?.account_type === 'LINKEDIN' &&
        ['CREDENTIALS', 'ERROR'].includes(data.AccountStatus?.message)
      ) {
        const { account_id: accountId } = data.AccountStatus;
        const users = await this.userRepository.find({
          where: {
            unipileAccountId: accountId,
            unipileAccountStatus: Not(UnipileAccountStatus.DISCONNECTED),
          },
        });

        await Promise.all(
          users.map(async (user) => {
            // Commented out to avoid unused variable warning
            /*const msg = {
              to: user.email,
              from: {
                name: 'Zileo',
                email: '<EMAIL>',
              },
              subject: 'Action Required: LinkedIn Connection Disconnected',
              html: `<!doctypehtml><meta charset=utf-8><meta content="ie=edge"http-equiv=x-ua-compatible><title>Your LinkedIn connection was disconnected</title><meta content="width=device-width,initial-scale=1"name=viewport><style>@media screen{@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:400;src:local('Source Sans Pro Regular'),local('SourceSansPro-Regular'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff')}@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:700;src:local('Source Sans Pro Bold'),local('SourceSansPro-Bold'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff')}}a,body,table,td{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}table,td{mso-table-rspace:0;mso-table-lspace:0}img{-ms-interpolation-mode:bicubic}a[x-apple-data-detectors]{font-family:inherit!important;font-size:inherit!important;font-weight:inherit!important;line-height:inherit!important;color:inherit!important;text-decoration:none!important}div[style*="margin: 16px 0;"]{margin:0!important}body{width:100%!important;height:100%!important;padding:0!important;margin:0!important}table{border-collapse:collapse!important}a{color:#1a82e2}img{height:auto;line-height:100%;text-decoration:none;border:0;outline:0}</style><body style=background-color:#e9ecef><div class=preheader style=display:none;max-width:0;max-height:0;overflow:hidden;font-size:1px;line-height:1px;color:#fff;opacity:0>A preheader is the short summary text that follows the subject line when an email is viewed in the inbox.</div><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:36px 24px 0;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;border-top:3px solid #d4dadf"><h1 style=margin:0;font-size:32px;font-weight:700;letter-spacing:-1px;line-height:48px>LinkedIn Connection Disconnected</h1></table><!--[if (gte mso 9)|(IE)]><![endif]--><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px"><p style=margin:0>Hi ${name},</p><br><p style=margin:0>Your LinkedIn account has been disconnected from our system. This may affect sequences with LinkedIn steps</p><tr><td align=left bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#ffffff style=padding:12px><table border=0 cellpadding=0 cellspacing=0><tr><td align=center bgcolor=#1a82e2 style=border-radius:6px><a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/settings?tab=linkedin" target=_blank style="display:inline-block;padding:16px 36px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;color:#fff;text-decoration:none;border-radius:6px">RECONNECT</a></table></table><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px"><p style=margin:0><strong>Steps to Reconnect:</strong><ul><li>Log in to our platform <a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}" target=_blank>Zileo</a><li>Go to the <a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/settings?tab=linkedin" target=_blank>LinkedIn connection</a> section<li>Follow the prompts to reconnect</ul></p><p style=margin:0><strong>Reactivate Sequences:</strong><ul><li>After reconnecting, manually reactivate the LinkedIn sequences at <a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/sequence" target=_blank>Sequence Tab</a></li></ul></p><p style=margin:0>We apologize for any inconvenience and appreciate your prompt action.</p><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px;border-bottom:3px solid #d4dadf"><p style=margin:0>Best regards,</p><p style=margin:0>Zileo Team</p></table><!--[if (gte mso 9)|(IE)]><![endif]--></table>`,
            };*/

            // await sgMail.send(msg).catch((warn) => {
            //   console.warn('Error in sending email', warn?.response?.body?.errors);
            // });
            // // Insert notification
            // await this.notificationRepository.insert(
            //   this.notificationRepository.create({
            //     userId: user.id,
            //     notificationType: NotificationEnum.LINKEDIN_DISCONNECTED,
            //     title: 'LinkedIn connection disconnected',
            //     content: 'This may affect sequences with LinkedIn steps. Please reconnect your LinkedIn account asap.',
            //   })
            // );
            // UPDATE connect status
            this.userRepository.update(user.id, {
              unipileAccountStatus: UnipileAccountStatus.DISCONNECTED,
            });
            // Stop SEQ
            // const sequences = await this.sequenceInstanceRepository
            //   .createQueryBuilder('si')
            //   .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
            //   .innerJoin('sequences', 's', 's.id = si.sequence_id')
            //   .select('si.sequence_id AS sequence_id')
            //   .where('s.status = :status AND ss.type = :type AND si.executedAt IS NULL AND s.user_id = :userId', {
            //     status: SequenceStatus.LIVE,
            //     type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
            //     userId: user.id,
            //   })
            //   .groupBy('si.sequence_id')
            //   .getRawMany();
            // const seqIds = sequences.map((item) => item.sequence_id);

            // await this.sequenceRepository.update({ id: In(seqIds) }, { status: SequenceStatus.STOP });

            // const activityLogs: Partial<SequenceActivityLogEntity>[] = seqIds.map((seqId) =>
            //   this.sequenceActivityLogRepository.create({
            //     type: SequenceActivityType.STOPPED,
            //     sequence: { id: seqId },
            //     content: { reason: 'LinkedIn connection was disconnected' },
            //     occurredAt: new Date().toISOString(),
            //   })
            // );

            // if (activityLogs.length) {
            //   await this.sequenceActivityLogRepository.insert(activityLogs);
            // }
          }),
        );
      }

      return this.formatOutputData({ key: 'UNIPILE_WEBHOOK' }, { data: {} });
    } catch (e) {
      console.error('ERROR unipileWebhook:', e.response?.body?.errors);

      return this.throwCommonMessage(
        'UNIPILE_WEBHOOK',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async getUnipileProfile(userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user || !user.unipileAccountId) {
        return this.throwCommonMessage(
          'UNIPILE_ACCOUNT_NOT_FOUND',
          new NotFoundException('Unipile account not found'),
        );
      }

      const data = await unipileClient.getProfile(user.unipileAccountId, 'me');

      if (user.linkedinAvatarUrl !== data?.profile_picture_url) {
        await this.userRepository.update(user.id, {
          linkedinAvatarUrl: data?.profile_picture_url,
        });
      }

      return this.formatOutputData({ key: 'GET_UNIPILE_PROFILE' }, { data: { data } });
    } catch (error) {
      console.error('Error in getUnipileProfile: ', error);
      return this.throwCommonMessage(
        'GET_UNIPILE_PROFILE',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async reconnectAccount(body: LinkedConnectionDto, userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      return this.throwCommonMessage(
        'LINKED_CONNECTION_USER_NOT_FOUND',
        new NotFoundException('User not found'),
      );
    }
    if (user.unipileAccountId) {
      try {
        await unipileClient.retrieveOwnProfile(user.unipileAccountId);
        // If retrieve data success - shouldn't reconnect
        return this.formatOutputData({ key: 'RECONNECT_SUCCESS' }, { data: {} });
      } catch (error) {
        // Do nothing
      }
    }

    try {
      const {
        username,
        password,
        accessToken,
        premiumToken,
        country,
        ip,
        userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      } = body;
      const payload: any = {
        ...(accessToken
          ? { access_token: accessToken, premium_token: premiumToken }
          : { username, password }),
        user_agent: userAgent,
        provider: unipileConfig.UNIPILE_PROVIDER,
      };
      if (country) {
        payload.country = country;
      }
      if (ip) {
        payload.ip = ip;
      }

      const data = await unipileClient.reconnectAccount(user.unipileAccountId, payload);
      if (data) {
        await this.userRepository.update(user.id, {
          unipileAccountStatus: UnipileAccountStatus.CONNECTED,
        });
      }

      return this.formatOutputData({ key: 'RECONNECT_SUCCESS' }, { data: { data } });
    } catch (e) {
      return this.throwCommonMessage(
        'RECONNECT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async getWorkingTimes(userId: string) {
    try {
      const workingTime = await this.userWorkingTimeRepository.findOne({
        where: { user: { id: userId } },
      });

      if (!workingTime) {
        return this.formatOutputData({ key: 'GET_WORKING_TIME_SUCCESS' }, { data: {} });
      }

      return this.formatOutputData({ key: 'GET_WORKING_TIME_SUCCESS' }, { data: workingTime });
    } catch (error) {
      console.error('Error in getWorkingTimes: ', error);

      return this.throwCommonMessage(
        'GET_WORKING_TIME_FAILED',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async updateWorkingTimes(userId: string, data: UpsertWorkingTimeDto) {
    try {
      await this.dataSource
        .createQueryBuilder()
        .insert()
        .into(UserWorkingTimeEntity)
        .values({ ...data, userId })
        .orUpdate(['working_hours', 'timezone', 'non_working_days'], ['user_id'])
        .execute();

      return this.formatOutputData(
        { key: 'UPDATE_WORKING_TIME_SUCCESS' },
        { data: { ...data, userId } },
      );
    } catch (error) {
      console.error('Error in updateWorkingTimes: ', error);

      return this.throwCommonMessage(
        'UPDATE_WORKING_TIME_FAILED',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async getInWorkingTimeUserIds(currentDate = new Date()) {
    try {
      const users = await this.dataSource
        .createQueryBuilder(UserEntity, 'user')
        .leftJoin('user_working_times', 'uwt', 'uwt.user_id = user.id')
        .select('user.id', 'id')
        .where('user.is_deleted = false')
        .addSelect('uwt.timezone', 'timezone')
        .addSelect('uwt.working_hours', 'working_hours')
        .addSelect('uwt.non_working_days', 'non_working_days')
        .getRawMany();

      // writeFileSync('1605.json', JSON.stringify({users}))
      const usersInWorkingTime = users.filter((user) => {
        if (!user.working_hours) {
          // User without working time record is always in working time
          return true;
        }
        const userTimezone = user.timezone || 'GMT';
        const dayOfWeek = moment.tz(currentDate, userTimezone).format('ddd').toUpperCase(); // e.g., 'MON'

        // Check if the current date is in any of the user's non-working days
        const nonWorkingDays = user.non_working_days || [];
        const isNonWorkingDay = nonWorkingDays.some((period: any) => {
          const start = moment.tz(period.start, userTimezone);
          const end = moment.tz(period.end, userTimezone);

          // Check if the current date is between the non-working period
          return moment.tz(currentDate, userTimezone).isBetween(start, end, null, '[]');
        });

        // If the current date is a non-working day, exclude the user
        if (isNonWorkingDay) {
          return false;
        }

        // Check the working hours for the given day
        const workingHours = user.working_hours[dayOfWeek];
        if (!workingHours) {
          // No working hours defined for the current day
          return false;
        }

        const { startTime, endTime } = workingHours;

        const currentTimeInUserTimezone = moment.tz(currentDate, userTimezone).format('HH:mm:ss');

        // Parse startTime and endTime in both 24-hour and AM/PM formats
        const startMoment = moment(startTime, ['HH:mm', 'hh:mm A']);
        const endMoment = moment(endTime, ['HH:mm', 'hh:mm A']);
        const currentMoment = moment(currentTimeInUserTimezone, 'HH:mm:ss');

        return currentMoment.isBetween(startMoment, endMoment, null, '[]');
      });

      return usersInWorkingTime.map((user) => user.id);
    } catch (error) {
      console.error('Error in getUserIdsByInWorkingTime: ', error);

      throw new InternalServerErrorException('Something went wrong');
    }
  }

  async linkUnipileAccount(userId: string) {
    try {
      await this.findById(userId);

      const link = await unipileClient.getOauthLink(userId);

      return link;
    } catch (error) {
      console.error('ERROR linkUnipileAccount:', error.response?.body?.errors);

      return this.throwCommonMessage(
        'UNIPILE_LINK_ACCOUNT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async unlinkUnipileAccount(userId: string) {
    try {
      const user = await this.findById(userId);
      if (!user.grantUnipileId) {
        return this.formatOutputData({ key: 'UNIPILE_UNLINKED_ACCOUNT' }, { data: {} });
      }

      await Promise.all([
        this.userRepository.update(userId, {
          grantUnipileId: null,
        }),
        unipileClient
          .deleteAccount(user.grantUnipileId)
          .then(() => true)
          .catch((error) => {
            if (error.response?.status !== 404) {
              throw error;
            }
          }),
      ]);

      return this.formatOutputData(
        { key: 'UNIPILE_UNLINKED_ACCOUNT' },
        { data: { success: true } },
      );
    } catch (error) {
      console.error('ERROR linkUnipileAccount:', error.response?.body?.errors);

      return this.throwCommonMessage(
        'UNIPILE_UNLINKED_ACCOUNT',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async saveUnipileLinkedAccount(userId: string, data: any) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user || data.status !== 'CREATION_SUCCESS' || !data[data.name]) {
        return this.throwCommonMessage(
          'UNIPILE_LINKED_ACCOUNT_WEBHOOK',
          new BadRequestException('Data invalid'),
        );
      }

      const accountId = data[data.name];

      await this.userRepository.update(user.id, {
        grantUnipileId: accountId,
      });

      return this.formatOutputData({ key: 'UNIPILE_LINKED_ACCOUNT_WEBHOOK' }, { data: {} });
    } catch (e) {
      console.error('ERROR unipileLinkedAccountWebhook:', e.response?.body?.errors);

      return this.throwCommonMessage(
        'UNIPILE_LINKED_ACCOUNT_WEBHOOK',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async onboardingCompany(body: OnboardingCompanyDto, userId: string) {
    try {
      const user = await this.validateRequestUser(userId);
      const isRequestFromSales = user.roleId === DefaultRoleIds.SALES;
      const { company, employees, subscription: subscriptionOptions } = body;

      // Validate company and employees
      await this.validateCompanyExists(company.name);
      const validatedEmployees = await this.validateAndPrepareEmployees(
        employees,
        company.licenseType,
        isRequestFromSales,
      );

      // Create company and assign licenses
      const companyId = await this.createCompanyWithLicenses(
        company,
        validatedEmployees,
        subscriptionOptions,
        userId,
        isRequestFromSales,
      );

      // Handle post-creation tasks
      await this.handlePostCreationTasks(
        validatedEmployees,
        employees,
        company.name,
        isRequestFromSales,
        user,
        companyId,
      );

      return this.formatOutputData({ key: 'ONBOARDING_COMPANY' }, { data: {} });
    } catch (error) {
      this.logger.error('Error in onboardingCompany:', error);
      return this.throwCommonMessage('ONBOARDING_COMPANY', error);
    }
  }

  private async validateRequestUser(userId: string): Promise<UserEntity> {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  private async validateCompanyExists(companyName: string): Promise<void> {
    const existingCompany = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .where({ name: companyName })
      .getOne();

    if (existingCompany) {
      throw new BadRequestException('This company already existed');
    }
  }

  private async validateAndPrepareEmployees(
    employees: OnboardingUserDto[],
    licenseType: LicenseType,
    isRequestFromSales: boolean,
  ): Promise<Partial<UserEntity>[]> {
    if (employees.length === 0) {
      return [];
    }

    // Validate admin requirement
    if (!employees.some((employee) => employee.role === RoleEnum.ADMIN)) {
      throw new BadRequestException('At least one employee must have the ADMIN role');
    }

    // Validate roles
    await this.validateEmployeeRoles(employees);

    // Validate usernames
    this.validateUsernames(employees);

    // Create user entities
    const createdUsers = await this.createUserEntities(employees, licenseType, isRequestFromSales);

    // Check for existing users
    return this.filterExistingUsers(createdUsers, employees);
  }

  private async validateEmployeeRoles(employees: OnboardingUserDto[]): Promise<void> {
    const roleKeyCodes = [...new Set(employees.map((item) => item.role))];

    if (roleKeyCodes.some((role) => [RoleEnum.SUPER_ADMIN, RoleEnum.SALES].includes(role))) {
      throw new BadRequestException('Role is not valid');
    }

    const roles = await this.dataSource
      .createQueryBuilder(RoleEntity, 'r')
      .where({ keyCode: In(roleKeyCodes) })
      .getMany();

    if (!roles.length) {
      throw new BadRequestException('Role is not valid');
    }
  }

  private validateUsernames(employees: OnboardingUserDto[]): void {
    const usernameRegex = /^[a-zA-Z0-9_.@]+$/;

    for (const employee of employees) {
      if (employee.username && !usernameRegex.test(employee.username)) {
        throw new BadRequestException(
          `Username '${employee.username}' can only contain letters, numbers, underscores, and periods`,
        );
      }
    }
  }

  private async createUserEntities(
    employees: OnboardingUserDto[],
    licenseType: LicenseType,
    isRequestFromSales: boolean,
  ): Promise<Partial<UserEntity>[]> {
    const roleKeyCodes = [...new Set(employees.map((item) => item.role))];
    const roles = await this.dataSource
      .createQueryBuilder(RoleEntity, 'r')
      .where({ keyCode: In(roleKeyCodes) })
      .getMany();

    const rolesMapping = convertArrayToObject(roles, 'keyCode');
    const hashedPassWords = await Promise.all(
      employees.map((item) => this.hashPassword(item.password)),
    );
    const hashedPassWordMapping = convertArrayToObject(hashedPassWords, 'password');

    return employees.map(({ email, password, username, role }) => {
      const status = this.determineUserStatus(role, isRequestFromSales);

      return {
        email,
        password: hashedPassWordMapping[password]?.hashPassword,
        username,
        roleId: rolesMapping[role]?.id,
        licenseType,
        status,
        initialPassword: password,
      };
    });
  }

  private determineUserStatus(role: RoleEnum, isRequestFromSales: boolean): UserStatus {
    if (isRequestFromSales) {
      return UserStatus.PENDING;
    }
    return role === RoleEnum.ADMIN ? UserStatus.ACTIVE : UserStatus.PENDING;
  }

  private async filterExistingUsers(
    createdUsers: Partial<UserEntity>[],
    employees: OnboardingUserDto[],
  ): Promise<Partial<UserEntity>[]> {
    const emails = createdUsers.map((user) => user.email.toLowerCase());
    const usernames = createdUsers.map((user) => user.username?.toLowerCase()).filter(Boolean);

    const [existingEmails, existingUsernames] = await Promise.all([
      this.checkExistingEmails(emails),
      this.checkExistingUsernames(usernames),
    ]);

    const existingEmailsSet = new Set(existingEmails.map((item) => item.email));
    const existingUsernamesSet = new Set(existingUsernames.map((item) => item.username));

    const filteredUsers = createdUsers.filter(
      (user) =>
        !existingEmailsSet.has(user.email?.toLowerCase()) &&
        !existingUsernamesSet.has(user.username?.toLowerCase()),
    );

    this.validateFilteredUsers(filteredUsers, existingEmails, existingUsernames);

    return filteredUsers;
  }

  private async checkExistingEmails(emails: string[]): Promise<{ email: string }[]> {
    return this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where('LOWER(email) IN (:...emails) AND is_deleted = false', { emails })
      .select('LOWER(email) AS email')
      .getRawMany();
  }

  private async checkExistingUsernames(usernames: string[]): Promise<{ username: string }[]> {
    if (usernames.length === 0) return [];

    return this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where('LOWER(username) IN (:...usernames) AND is_deleted = false', { usernames })
      .select('LOWER(username) AS username')
      .getRawMany();
  }

  private validateFilteredUsers(
    filteredUsers: Partial<UserEntity>[],
    existingEmails: { email: string }[],
    existingUsernames: { username: string }[],
  ): void {
    if (filteredUsers.length === 0) {
      const errorMessages = [];

      if (existingEmails.length > 0) {
        errorMessages.push(
          `Email ${existingEmails.map((item) => item.email).join(', ')} already existed`,
        );
      }

      if (existingUsernames.length > 0) {
        errorMessages.push(
          `Username ${existingUsernames.map((item) => item.username).join(', ')} already existed`,
        );
      }

      throw new BadRequestException(errorMessages.join(', and '));
    }
  }

  private async createCompanyWithLicenses(
    company: CreateOrganizationDto,
    validatedEmployees: Partial<UserEntity>[],
    subscriptionOptions: SubscriptionOptionsDto,
    userId: string,
    isRequestFromSales: boolean,
  ): Promise<string> {
    return this.dataSource.transaction(async (entityManager) => {
      // Create company
      const companyId = await this.createCompany(
        company,
        userId,
        isRequestFromSales,
        entityManager,
      );

      // Validate license requirements before creating anything
      await this.validateLicenseRequirements(validatedEmployees, subscriptionOptions);

      // Create subscription with licenses first
      const subscription = await this.createSubscriptionWithLicenses(
        companyId,
        subscriptionOptions,
        userId,
        entityManager,
      );

      // Create users after subscription and licenses are ready
      const createdUsers = await this.createUsers(validatedEmployees, companyId, entityManager);

      // Assign licenses to users
      await this.assignLicensesToUsers(subscription.id, createdUsers, userId, entityManager);

      this.logger.log(
        `Successfully created company ${companyId} with ${createdUsers.length} users`,
      );
      return companyId;
    });
  }

  private async createCompany(
    company: CreateOrganizationDto,
    userId: string,
    isRequestFromSales: boolean,
    entityManager: any,
  ): Promise<string> {
    const result = await entityManager
      .createQueryBuilder(OrganizationEntity, 'o')
      .insert()
      .values({
        ...company,
        companyIndustries: company?.companyIndustries?.join(', '),
        companyTypes: company?.companyTypes?.join(','),
        companyAdmins: company?.companyAdmins?.join(','),
        tags: company?.tags?.join(','),
        status: isRequestFromSales
          ? OrganizationStatusEnum.PENDING
          : OrganizationStatusEnum.APPROVED,
        createdBy: userId,
      })
      .execute();

    return result.raw?.[0]?.id;
  }

  private async createUsers(
    validatedEmployees: Partial<UserEntity>[],
    companyId: string,
    entityManager: any,
  ): Promise<UserEntity[]> {
    return entityManager.save(
      UserEntity,
      validatedEmployees.map((user) => ({ ...user, organizationId: companyId })),
    );
  }

  private validateLicenseRequirements(
    validatedEmployees: Partial<UserEntity>[],
    subscriptionOptions: SubscriptionOptionsDto,
  ): void {
    // Check admin requirement - this should already be validated in validateAndPrepareEmployees
    // but we double-check here for safety
    if (validatedEmployees.length === 0) {
      throw new BadRequestException('No valid employees to create');
    }

    // Validate license count
    if (validatedEmployees.length > subscriptionOptions.licenseCount) {
      throw new BadRequestException(
        `Number of users (${validatedEmployees.length}) exceeds the purchased license count (${subscriptionOptions.licenseCount})`,
      );
    }
  }

  private async createSubscriptionWithLicenses(
    companyId: string,
    subscriptionOptions: SubscriptionOptionsDto,
    userId: string,
    entityManager: any,
  ): Promise<SubscriptionEntity> {
    return this.subscriptionService.createSubscription(
      companyId,
      subscriptionOptions.planId,
      subscriptionOptions.licenseCount,
      subscriptionOptions.billingCycle,
      subscriptionOptions.isTrial || false,
      userId,
      entityManager,
    );
  }

  private async assignLicensesToUsers(
    subscriptionId: string,
    createdUsers: UserEntity[],
    userId: string,
    entityManager: any,
  ): Promise<void> {
    const availableLicenses = await this.licenseService.getAvailableLicenses(subscriptionId);

    // Separate admin and non-admin users
    const adminUsers = createdUsers.filter(
      (user) => user.roleId && user.roleId.toString().includes('admin'),
    );
    const nonAdminUsers = createdUsers.filter(
      (user) => !user.roleId || !user.roleId.toString().includes('admin'),
    );

    let licenseIndex = 0;

    // Assign licenses to admin users first
    await BBPromise.map(adminUsers, async (adminUser) => {
      if (licenseIndex < availableLicenses.length) {
        await this.licenseService.assignLicenseToUser(
          availableLicenses[licenseIndex].id,
          adminUser.id,
          userId,
          entityManager,
        );
        licenseIndex += 1;
        this.logger.log(`Assigned license to admin user ${adminUser.id}`);
      }
    });

    // Assign remaining licenses to non-admin users
    await BBPromise.map(nonAdminUsers, async (nonAdminUser) => {
      if (licenseIndex < availableLicenses.length) {
        await this.licenseService.assignLicenseToUser(
          availableLicenses[licenseIndex].id,
          nonAdminUser.id,
          userId,
          entityManager,
        );
        licenseIndex += 1;
        this.logger.log(`Assigned license to user ${nonAdminUser.id}`);
      }
    });

    this.logger.log(
      `Assigned ${licenseIndex} licenses out of ${availableLicenses.length} available licenses`,
    );
  }

  private async handlePostCreationTasks(
    validatedEmployees: Partial<UserEntity>[],
    originalEmployees: OnboardingUserDto[],
    companyName: string,
    isRequestFromSales: boolean,
    requestUser: UserEntity,
    companyId: string,
  ): Promise<void> {
    // // Skip email sending in dev environment for test companies
    // if (process.env.APP_ENV === 'dev' && !companyName.startsWith('[TEST]')) {
    //   return;
    // }

    // Send onboarding emails to admin users (if not from sales)
    if (!isRequestFromSales && validatedEmployees.length > 0) {
      await this.sendOnboardingEmails(validatedEmployees, originalEmployees);
    }

    // Send notification to Super Admin (if from sales)
    // TODO: Temporarily disabled subscription notifications
    // if (isRequestFromSales) {
    //   await this.sendSuperAdminNotification(requestUser, companyId);
    // }
  }

  private async sendOnboardingEmails(
    validatedEmployees: Partial<UserEntity>[],
    originalEmployees: OnboardingUserDto[],
  ): Promise<void> {
    const adminUsers = validatedEmployees.filter((user) => {
      const originalEmployee = originalEmployees.find(
        (emp) => emp.email.toLowerCase() === user.email?.toLowerCase(),
      );
      return originalEmployee && originalEmployee.role === RoleEnum.ADMIN;
    });

    if (adminUsers.length > 0) {
      const adminEmails = adminUsers.map((user) => {
        const originalEmployee = originalEmployees.find(
          (emp) => emp.email.toLowerCase() === user.email?.toLowerCase(),
        );
        return {
          toEmail: user.email,
          password: originalEmployee?.password || '',
        };
      });

      await this.mailService.bulkSendOnboardingEmails(adminEmails);
    }
  }

  private async sendSuperAdminNotification(
    requestUser: UserEntity,
    companyId: string,
  ): Promise<void> {
    const superAdmin = await this.userRepository.findOneBy({ roleId: DefaultRoleIds.SUPER_ADMIN });

    if (superAdmin) {
      await this.notificationService.createNotification({
        notificationType: NotificationEnum.ONBOARDING_COMPANY_REQUEST,
        userId: superAdmin.id,
        creatorId: requestUser.id,
        title: 'New company onboarding request',
        content: `New company onboarding request from ${requestUser.username} (${requestUser.email})`,
        targetId: companyId,
      });
    }
  }

  async updateOnboardingCompanyStatus(
    { status, companyId }: { status: OrganizationStatusEnum; companyId: string },
    userId: string,
  ) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const company = await this.getDetailOrganization(companyId);

      if (!company) {
        throw new NotFoundException('Company not found or deleted');
      }

      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);

      // Update company status
      await organizationRepository
        .createQueryBuilder()
        .update(OrganizationEntity)
        .set({
          status,
        })
        .where('id = :companyId', { companyId })
        .execute();

      const employees = [...(company?.result?.users || [])];

      if (employees?.length > 0) {
        // Get users with their roles
        const usersWithRoles = await this.userRepository.find({
          where: { id: In(employees.map((item) => item?.id)) },
          relations: { role: true },
        });

        // Separate admin users from regular users
        const adminUsers = usersWithRoles.filter(
          (userItem) => userItem.role.keyCode === RoleEnum.ADMIN,
        );
        const regularUsers = usersWithRoles.filter(
          (userItem) => userItem.role.keyCode !== RoleEnum.ADMIN,
        );

        // Set status based on role and company status
        if (status === OrganizationStatusEnum.APPROVED) {
          // Assign licenses to users and activate admin users
          await this.assignLicensesToApprovedCompanyUsers(
            companyId,
            adminUsers,
            regularUsers,
            userId,
          );
        } else {
          // If company is not approved, set all users to DEACTIVATED
          await this.userRepository.update(
            { id: In(usersWithRoles.map((userItem) => userItem.id)) },
            { status: UserStatus.DEACTIVATED },
          );
        }

        // Send email only to ADMIN users when approving a company
        if (status === OrganizationStatusEnum.APPROVED && adminUsers.length > 0) {
          const adminEmails = adminUsers.map((adminUser) => {
            const employeeData = employees.find((emp) => emp.id === adminUser.id);
            return {
              toEmail: adminUser.email,
              password: employeeData?.initialPassword || '',
            };
          });

          await this.mailService.bulkSendOnboardingEmails(adminEmails);
        }
      }

      // TODO: Temporarily disabled subscription notifications
      // await this.notificationService.createNotification({
      //   notificationType: NotificationEnum.ONBOARDING_COMPANY_REQUEST,
      //   userId: company?.result?.createdBy,
      //   creatorId: userId,
      //   title: 'New company onboarding request',
      //   content: `Your onboarding company request was ${status?.toLowerCase()} by ${user.username} (SUPER ADMIN)`,
      //   targetId: companyId,
      // });

      return this.formatOutputData({ key: 'UPDATE_STATUS_ONBOARDING_COMPANY' }, { data: {} });
    } catch (e) {
      console.error('ERROR updateOnboardingCompanyStatus errors:', e.response?.body?.errors);
      console.error('ERROR updateOnboardingCompanyStatus body:', e.response?.body);
      console.error('ERROR updateOnboardingCompanyStatus response:', e.response);
      console.error('ERROR updateOnboardingCompanyStatus:', e);

      return this.throwCommonMessage('UPDATE_STATUS_ONBOARDING_COMPANY', e);
    }
  }

  async singleOnboardingEmployee(body: SingleOnboardingUserDto, userId: string) {
    try {
      const user = await this.validateRequestUser(userId);
      const { email, password, username, role, organizationId, originalCredits } = body;
      const currentUserRole = user.role.keyCode;

      // Validate role permissions
      if (
        [RoleEnum.SUPER_ADMIN, RoleEnum.SALES].includes(role) &&
        currentUserRole !== RoleEnum.SUPER_ADMIN
      ) {
        throw new BadRequestException('Only super admin can add new employee with any role');
      }

      // Validate role exists
      const roleEntity = await this.roleRepository.findOneBy({ keyCode: role });
      if (!roleEntity) {
        throw new BadRequestException('Role is not valid');
      }

      // Validate company exists
      const company = await this.dataSource
        .createQueryBuilder(OrganizationEntity, 'o')
        .where({ id: organizationId })
        .getOne();

      if (!company) {
        throw new BadRequestException('This company has not been existed');
      }

      // Check license availability before creating user
      const availableLicenseCount =
        await this.licenseService.getAvailableLicenseCount(organizationId);
      if (availableLicenseCount === 0) {
        throw new BadRequestException(
          'No available licenses for this organization. Please purchase more licenses or unassign existing ones.',
        );
      }

      // Validate username format
      if (username && !username.match(/^[a-zA-Z0-9_.@]+$/)) {
        throw new BadRequestException(
          'Username can only contain letters, numbers, underscores, and periods',
        );
      }

      // Check for existing users
      await this.validateUserUniqueness(email, username);

      // Create user in transaction to ensure license assignment
      const newCreatedUser = await this.dataSource.transaction(async (entityManager) => {
        // Hash password
        const hashedPassWord = await this.hashPassword(password);

        // Determine user status
        const status = this.determineSingleUserStatus(role, company.status);

        // Create user entity
        const createdUser: Partial<UserEntity> = {
          email,
          password: hashedPassWord.hashPassword,
          username,
          roleId: roleEntity.id,
          status,
          initialPassword: password,
          organizationId,
          licenseType: LicenseType.STANDARD,
        };

        // Save user
        const savedUser = await entityManager.save(UserEntity, createdUser);

        // Get subscription and assign license
        const subscription =
          await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);
        if (subscription) {
          // Get available licenses
          const availableLicenses = await this.licenseService.getAvailableLicenses(subscription.id);

          if (availableLicenses.length > 0) {
            // Assign license to user (credits will be automatically allocated)
            await this.licenseService.assignLicenseToUser(
              availableLicenses[0].id,
              savedUser.id,
              userId,
              entityManager,
            );
            this.logger.log(`Assigned license ${availableLicenses[0].id} to user ${savedUser.id}`);
          } else {
            throw new BadRequestException('No available licenses found for assignment');
          }
        } else if (currentUserRole !== RoleEnum.SUPER_ADMIN) {
          throw new NotFoundException(
            'Cannot create user as there is no active subscription for this organization',
          );
        }

        return savedUser;
      });

      // Handle post-creation tasks
      await this.handleSingleUserPostCreation(
        newCreatedUser,
        company,
        role,
        email,
        password,
        originalCredits,
        userId,
        currentUserRole,
        organizationId,
      );

      return this.formatOutputData({ key: 'SINGLE_ONBOARDING_EMPLOYEE' }, { data: newCreatedUser });
    } catch (error) {
      this.logger.error('Error in singleOnboardingEmployee:', error);
      return this.throwCommonMessage('SINGLE_ONBOARDING_EMPLOYEE', error);
    }
  }

  private async validateUserUniqueness(email: string, username?: string): Promise<void> {
    // Check for existing email (case insensitive)
    const existingEmail = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where('LOWER(u.email) = LOWER(:email) AND u.is_deleted = false', { email })
      .getOne();

    if (existingEmail) {
      throw new BadRequestException('Email already existed');
    }

    // Check for existing username (case insensitive)
    if (username) {
      const existingUsername = await this.dataSource
        .createQueryBuilder(UserEntity, 'u')
        .where('LOWER(u.username) = LOWER(:username) AND u.is_deleted = false', { username })
        .getOne();

      if (existingUsername) {
        throw new BadRequestException('Username already existed');
      }
    }
  }

  private determineSingleUserStatus(
    role: RoleEnum,
    companyStatus: OrganizationStatusEnum,
  ): UserStatus {
    if (companyStatus === OrganizationStatusEnum.APPROVED) {
      // If company is approved, only ADMIN users are ACTIVE, others are PENDING
      return role === RoleEnum.ADMIN ? UserStatus.ACTIVE : UserStatus.PENDING;
    }
    // If company is not approved, all users are PENDING
    return UserStatus.PENDING;
  }

  private async handleSingleUserPostCreation(
    newCreatedUser: UserEntity,
    company: OrganizationEntity,
    role: RoleEnum,
    email: string,
    password: string,
    originalCredits: number,
    userId: string,
    currentUserRole: RoleEnum,
    organizationId: string,
  ): Promise<void> {
    // Handle legacy credit allocation if specified (for backward compatibility)
    // Note: Credits are already auto-allocated when license is assigned
    if (originalCredits > 0) {
      this.logger.warn(
        `Legacy credit allocation requested (${originalCredits} credits) for user ${newCreatedUser.id}. ` +
          `Credits are now automatically allocated through license assignment based on subscription plan.`,
      );

      // Optional: Still support legacy allocation for special cases
      const userCreditsMap = {
        [newCreatedUser.id]: originalCredits,
      };

      const currentSubscription =
        await this.subscriptionService.getSubscriptionForOrganization(organizationId);
      if (currentSubscription) {
        await this.subscriptionService.allocateCreditsToMultipleUsers(
          currentSubscription.id,
          userCreditsMap,
          userId,
        );
      } else if (currentUserRole !== RoleEnum.SUPER_ADMIN) {
        this.logger.warn(
          `Cannot allocate legacy credits to user ${newCreatedUser.id} as there is no subscription for organization ${organizationId}`,
        );
      }
    }

    // Send email only if the user is an ADMIN and company is approved
    if (company.status === OrganizationStatusEnum.APPROVED && role === RoleEnum.ADMIN) {
      await this.mailService.bulkSendOnboardingEmails([{ toEmail: email, password }]);
    }
  }

  private async assignLicensesToApprovedCompanyUsers(
    companyId: string,
    adminUsers: any[],
    regularUsers: any[],
    performedBy: string,
  ): Promise<void> {
    try {
      // Get active subscription for the company
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(companyId);
      if (!subscription) {
        this.logger.warn(
          `No active subscription found for company ${companyId}. Users activated without licenses.`,
        );

        // Still activate admin users even without subscription
        if (adminUsers.length > 0) {
          await this.userRepository.update(
            { id: In(adminUsers.map((adminUser) => adminUser.id)) },
            { status: UserStatus.ACTIVE },
          );
        }

        // Keep regular users as PENDING
        if (regularUsers.length > 0) {
          await this.userRepository.update(
            { id: In(regularUsers.map((regularUser) => regularUser.id)) },
            { status: UserStatus.PENDING },
          );
        }
        return;
      }

      // Get available licenses
      const availableLicenses = await this.licenseService.getAvailableLicenses(subscription.id);
      const totalUsers = adminUsers.length + regularUsers.length;

      if (availableLicenses.length < totalUsers) {
        this.logger.warn(
          `Insufficient licenses for company ${companyId}. Available: ${availableLicenses.length}, Required: ${totalUsers}`,
        );
      }

      let licenseIndex = 0;

      // Assign licenses to admin users first and activate them
      for (const adminUser of adminUsers) {
        if (licenseIndex < availableLicenses.length) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[licenseIndex].id,
            adminUser.id,
            performedBy,
          );
          licenseIndex++;
          this.logger.log(`Assigned license to admin user ${adminUser.id}`);
        }

        // Activate admin user (regardless of license assignment)
        await this.userRepository.update({ id: adminUser.id }, { status: UserStatus.ACTIVE });
      }

      // Assign licenses to regular users but keep them PENDING
      for (const regularUser of regularUsers) {
        if (licenseIndex < availableLicenses.length) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[licenseIndex].id,
            regularUser.id,
            performedBy,
          );
          licenseIndex++;
          this.logger.log(`Assigned license to regular user ${regularUser.id}`);
        }

        // Keep regular users as PENDING (will be activated when admin completes onboarding)
        await this.userRepository.update({ id: regularUser.id }, { status: UserStatus.PENDING });
      }

      this.logger.log(
        `Company ${companyId} approved: Assigned ${licenseIndex} licenses, activated ${adminUsers.length} admin users, ${regularUsers.length} regular users remain pending`,
      );
    } catch (error) {
      this.logger.error(
        `Error assigning licenses to approved company users: ${error.message}`,
        error.stack,
      );

      // Fallback: Still activate admin users even if license assignment fails
      if (adminUsers.length > 0) {
        await this.userRepository.update(
          { id: In(adminUsers.map((adminUser) => adminUser.id)) },
          { status: UserStatus.ACTIVE },
        );
      }

      if (regularUsers.length > 0) {
        await this.userRepository.update(
          { id: In(regularUsers.map((regularUser) => regularUser.id)) },
          { status: UserStatus.PENDING },
        );
      }

      throw error;
    }
  }

  private async assignLicensesToPendingUsers(
    organizationId: string,
    pendingUsers: any[],
    performedBy: string,
  ): Promise<void> {
    try {
      // Get active subscription for the organization
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);
      if (!subscription) {
        this.logger.warn(
          `No active subscription found for organization ${organizationId}. Users activated without licenses.`,
        );

        // Still activate users even without subscription
        await this.userRepository.update(
          { id: In(pendingUsers.map((user) => user.id)) },
          { status: UserStatus.ACTIVE },
        );
        return;
      }

      // Get available licenses
      const availableLicenses = await this.licenseService.getAvailableLicenses(subscription.id);

      if (availableLicenses.length < pendingUsers.length) {
        this.logger.warn(
          `Insufficient licenses for organization ${organizationId}. Available: ${availableLicenses.length}, Required: ${pendingUsers.length}`,
        );
      }

      let licenseIndex = 0;

      // Assign licenses to pending users and activate them
      for (const pendingUser of pendingUsers) {
        if (licenseIndex < availableLicenses.length) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[licenseIndex].id,
            pendingUser.id,
            performedBy,
          );
          licenseIndex++;
          this.logger.log(`Assigned license to pending user ${pendingUser.id}`);
        }

        // Activate user (regardless of license assignment)
        await this.userRepository.update({ id: pendingUser.id }, { status: UserStatus.ACTIVE });
      }

      this.logger.log(
        `Admin onboarding completed for organization ${organizationId}: Assigned ${licenseIndex} licenses, activated ${pendingUsers.length} pending users`,
      );
    } catch (error) {
      this.logger.error(`Error assigning licenses to pending users: ${error.message}`, error.stack);

      // Fallback: Still activate users even if license assignment fails
      await this.userRepository.update(
        { id: In(pendingUsers.map((user) => user.id)) },
        { status: UserStatus.ACTIVE },
      );

      throw error;
    }
  }

  private calculateRoleCredits(users, credits) {
    const roleTotals = {};
    users.forEach((user) => {
      const roleName = user.role.keyCode;
      if (!roleTotals[roleName]) {
        roleTotals[roleName] = {
          totalOriginalAmount: 0,
          totalRemaining: 0,
          totalUsed: 0,
        };
      }

      const userCredits = credits.find((credit) => credit.userId === user.id);

      if (userCredits) {
        roleTotals[roleName].totalOriginalAmount += userCredits?.originalAmount || 0;
        roleTotals[roleName].totalRemaining += userCredits?.remaining;
      }
      roleTotals[roleName].totalUsed =
        roleTotals[roleName].totalOriginalAmount - roleTotals[roleName].totalRemaining;
    });

    return roleTotals;
  }

  private calculateSystemRoleCredits(organizations: any[]) {
    const systemRoleTotals = {};

    organizations.forEach((org) => {
      org.users?.forEach((user) => {
        const roleName = user.role?.keyCode || 'UNKNOWN';
        if (!systemRoleTotals[roleName]) {
          systemRoleTotals[roleName] = {
            totalOriginalAmount: 0,
            totalRemaining: 0,
            totalUsed: 0,
            userCount: 0,
          };
        }

        systemRoleTotals[roleName].totalOriginalAmount += user.totalCredits || 0;
        systemRoleTotals[roleName].totalRemaining += user.remainingCredits || 0;
        systemRoleTotals[roleName].totalUsed += user.usedCredits || 0;
        systemRoleTotals[roleName].userCount += 1;
      });
    });

    return systemRoleTotals;
  }

  private async getLowCreditCompaniesCount(threshold: number) {
    // Simple count query for companies with low TOPUP credits (exclude plan credits)
    const count = await this.dataSource
      .getRepository(OrganizationEntity)
      .createQueryBuilder('org')
      .leftJoin(
        OrganizationQuotaEntity,
        'quota',
        'quota.organizationId = org.id AND quota.featureId = :featureId AND quota.source = :source',
        {
          featureId: 'credits',
          source: QuotaSource.TOPUP,
        },
      )
      .where('org.status = :status', { status: OrganizationStatusEnum.APPROVED })
      .andWhere('(quota.remaining IS NULL OR quota.remaining < :threshold)', { threshold })
      .getCount();

    return count;
  }

  async getCompanyCreditManagement(orgId: string | null, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true },
      });
      // Check permissions
      const canViewSystemData = [RoleEnum.SUPER_ADMIN, RoleEnum.SALES].includes(user.role.keyCode);

      // If orgId is null, return system-wide data for Super Admin/Sales
      if (!orgId && canViewSystemData) {
        return this.getSystemCreditManagementData();
      }

      const organizationId = orgId || user.organizationId;

      // Organization-specific data
      const listUsers = await this.userRepository.find({
        where: { organizationId },
        relations: { role: true },
        select: ['id', 'fullName', 'email', 'role', 'organizationId'],
      });
      const listUserId = listUsers.map((item) => item.id);

      // Get only TOPUP user credits (exclude plan credits)
      const creditManagement = await this.userQuotaRepository.find({
        where: { userId: In(listUserId), featureId: 'credits', source: QuotaSource.TOPUP },
        select: ['id', 'originalAmount', 'remaining', 'userId'],
      });
      // Get ALL TOPUP organization credits (exclude plan credits) and sum them
      const organizationCredits = await this.organizationQuotaRepository.find({
        where: { featureId: 'credits', organizationId, source: QuotaSource.TOPUP },
        select: ['remaining', 'originalAmount'],
      });

      // Sum all TOPUP credits from multiple topup transactions
      const organizationCredit = organizationCredits.reduce(
        (total, credit) => ({
          remaining: total.remaining + (credit.remaining || 0),
          originalAmount: total.originalAmount + (credit.originalAmount || 0),
        }),
        { remaining: 0, originalAmount: 0 },
      );

      const creditManagementByRole = this.calculateRoleCredits(listUsers, creditManagement);

      const initCreditManagement = {
        remaining: 0,
        originalAmount: 0,
      };

      // Get user credit usage information
      const userCreditsUsage = await Promise.all(
        listUsers.map(async (user) => {
          const userQuota = creditManagement?.find((cr) => cr.userId == user.id);
          const totalCredits = userQuota?.originalAmount || 0;
          const remainingCredits = userQuota?.remaining || 0;
          const usedCredits = totalCredits - remainingCredits;

          return {
            ...user,
            creditManagement: userQuota,
            totalCredits,
            usedCredits,
          };
        }),
      );

      const mappedUser = userCreditsUsage || initCreditManagement;

      const payload = {
        // Common fields
        creditManagementByRole,
        staffs: mappedUser,
        organizationCredit: {
          ...organizationCredit,
          // Add organization-level credit fields
          used: (organizationCredit?.originalAmount || 0) - (organizationCredit?.remaining || 0),
          total: organizationCredit?.originalAmount || 0,
        },

        // Organization-specific fields
        type: 'organization',
        organizationId,
        organizationSummary: {
          totalCreditsAllocated: organizationCredit?.originalAmount || 0,
          totalCreditsRemaining: organizationCredit?.remaining || 0,
          totalCreditsUsed:
            (organizationCredit?.originalAmount || 0) - (organizationCredit?.remaining || 0),
          totalUsers: listUsers.length,
        },
      };

      return this.formatOutputData({ key: 'GET_COMPANY_CREDIT_MANAGEMENT' }, { data: payload });
    } catch (e) {
      return this.throwCommonMessage('GET_COMPANY_CREDIT_MANAGEMENT', e);
    }
  }

  async getCreditStats(orgId: string | null, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true },
      });

      if (!user) {
        throw new BadRequestException('Profile not found');
      }

      // Check if user has permission to view system-wide stats
      const canViewSystemStats =
        user.role.keyCode === RoleEnum.SUPER_ADMIN || user.role.keyCode === RoleEnum.SALES;

      // If Super Admin/Sales and no specific orgId provided, show system stats
      if (canViewSystemStats && !orgId) {
        // Return system-wide stats for Super Admin and Sales
        return this.getSystemCreditStatsData();
      } else {
        // Return organization-specific stats for other roles or when orgId is specified
        return this.getOrganizationCreditStatsData(orgId || user.organizationId, user);
      }
    } catch (e) {
      return this.throwCommonMessage('GET_CREDIT_STATS', e);
    }
  }

  private async getSystemCreditStatsData() {
    const LOW_CREDIT_THRESHOLD = 500; // Constant for low credit threshold

    const [
      totalOrganizations,
      totalUsers,
      orgQuotas,
      totalCreditsUsed,
      totalCreditsTopup,
      totalRevenue,
      lowCreditCompaniesCount,
    ] = await Promise.all([
      // Total organizations
      this.dataSource.getRepository(OrganizationEntity).count({
        where: { status: OrganizationStatusEnum.APPROVED },
      }),

      // Total active users
      this.userRepository.count({
        where: { isDeleted: false },
      }),

      // Get only TOPUP organization quotas for credits (exclude plan credits)
      this.organizationQuotaRepository.find({
        where: { featureId: 'credits', source: QuotaSource.TOPUP },
        select: ['organizationId', 'originalAmount', 'remaining'],
      }),

      // Total credits used (from usage logs - only actual usage, not allocations)
      this.dataSource
        .getRepository('quota_usage_logs')
        .createQueryBuilder('qul')
        .select('SUM(qul.amount)', 'total')
        .where('qul.featureId = :featureId', { featureId: 'credits' })
        .andWhere('qul.logType = :logType', { logType: 'usage' })
        .andWhere('qul.quotaSource = :quotaSource', { quotaSource: QuotaSource.TOPUP })
        .getRawOne()
        .then((result) => parseFloat(result.total) || 0),

      // Total credits from topup (revenue source)
      this.dataSource
        .getRepository(OrganizationQuotaEntity)
        .createQueryBuilder('oq')
        .select('SUM(oq.originalAmount)', 'total')
        .where('oq.featureId = :featureId', { featureId: 'credits' })
        .andWhere('oq.source = :source', { source: QuotaSource.TOPUP })
        .getRawOne()
        .then((result) => parseFloat(result.total) || 0),

      // Total revenue (assuming $1 per credit for topups)
      this.dataSource
        .getRepository(OrganizationQuotaEntity)
        .createQueryBuilder('oq')
        .select('SUM(oq.originalAmount)', 'total')
        .where('oq.featureId = :featureId', { featureId: 'credits' })
        .andWhere('oq.source = :source', { source: QuotaSource.TOPUP })
        .getRawOne()
        .then((result) => parseFloat(result.total) || 0), // Same as topup credits since $1 per credit

      // Count of companies with low credits (< 500)
      this.getLowCreditCompaniesCount(LOW_CREDIT_THRESHOLD),
    ]);

    // Calculate totals from orgQuotas (same as management endpoint)
    const totalCreditsAllocated = orgQuotas.reduce((sum, q) => sum + (q.originalAmount || 0), 0);
    const totalCreditsRemaining = orgQuotas.reduce((sum, q) => sum + (q.remaining || 0), 0);

    const data = {
      // Common fields (consistent with credit management)
      creditManagementByRole: {}, // Empty for stats endpoint
      staffs: [], // Empty for stats endpoint - no detailed company data
      organizationCredit: {
        remaining: totalCreditsRemaining, // Use same logic as management endpoint
        originalAmount: totalCreditsAllocated,
      },

      // System-specific fields
      type: 'system',
      systemSummary: {
        totalOrganizations,
        totalUsers,
        totalCreditsAllocated,
        totalCreditsRemaining,
        totalCreditsUsed,
        totalCreditsTopup,
        totalRevenue: totalRevenue,
        averageRevenuePerOrganization:
          totalOrganizations > 0 ? totalRevenue / totalOrganizations : 0,
        lowCreditCompaniesCount: lowCreditCompaniesCount,
      },

      // Sales/Super Admin specific fields
      lowCreditThreshold: 500,
    };

    return this.formatOutputData({ key: 'GET_CREDIT_STATS_SUCCESS' }, { data });
  }

  private async getSystemCreditManagementData() {
    const LOW_CREDIT_THRESHOLD = 500; // Constant for low credit threshold

    // Get all organizations with their users and credit data
    const [organizations, lowCreditCompaniesCount] = await Promise.all([
      this.dataSource
        .getRepository(OrganizationEntity)
        .createQueryBuilder('org')
        .leftJoinAndSelect('org.users', 'user', 'user.isDeleted = false')
        .leftJoinAndSelect('user.role', 'role')
        .where('org.status = :status', { status: OrganizationStatusEnum.APPROVED })
        .select([
          'org.id',
          'org.name',
          'org.status',
          'user.id',
          'user.fullName',
          'user.email',
          'role.name',
          'role.keyCode',
        ])
        .getMany(),

      // Get count of companies with low credits
      this.getLowCreditCompaniesCount(LOW_CREDIT_THRESHOLD),
    ]);

    // Get only TOPUP organization quotas for credits (exclude plan credits)
    const orgQuotas = await this.organizationQuotaRepository.find({
      where: { featureId: 'credits', source: QuotaSource.TOPUP },
      select: ['organizationId', 'originalAmount', 'remaining'],
    });

    // Get only TOPUP user quotas for credits (exclude plan credits)
    const userQuotas = await this.userQuotaRepository.find({
      where: { featureId: 'credits', source: QuotaSource.TOPUP },
      select: ['userId', 'originalAmount', 'remaining'],
    });

    // Map organizations with their credit data (sum from multiple TOPUP records)
    const organizationsWithCredits = organizations.map((org) => {
      // Sum all TOPUP organization quotas for this org
      const orgQuotasForOrg = orgQuotas.filter((q) => q.organizationId === org.id);
      const totalOrgCredits = orgQuotasForOrg.reduce(
        (sum, quota) => ({
          originalAmount: sum.originalAmount + (quota.originalAmount || 0),
          remaining: sum.remaining + (quota.remaining || 0),
        }),
        { originalAmount: 0, remaining: 0 },
      );

      const orgUserIds = org.users?.map((u) => u.id) || [];
      const orgUserQuotas = userQuotas.filter((uq) => orgUserIds.includes(uq.userId));

      const usersWithCredits =
        org.users?.map((user) => {
          // Sum all TOPUP user quotas for this user
          const userQuotasForUser = orgUserQuotas.filter((uq) => uq.userId === user.id);
          const totalUserCredits = userQuotasForUser.reduce(
            (sum, quota) => ({
              originalAmount: sum.originalAmount + (quota.originalAmount || 0),
              remaining: sum.remaining + (quota.remaining || 0),
            }),
            { originalAmount: 0, remaining: 0 },
          );

          return {
            ...user,
            totalCredits: totalUserCredits.originalAmount,
            usedCredits: totalUserCredits.originalAmount - totalUserCredits.remaining,
            remainingCredits: totalUserCredits.remaining,
          };
        }) || [];

      return {
        id: org.id,
        name: org.name,
        status: org.status,
        totalCreditsAllocated: totalOrgCredits.originalAmount,
        totalCreditsRemaining: totalOrgCredits.remaining,
        totalCreditsUsed: totalOrgCredits.originalAmount - totalOrgCredits.remaining,
        totalUsers: org.users?.length || 0,
        users: usersWithCredits,
      };
    });

    const payload = {
      // Common fields
      creditManagementByRole: this.calculateSystemRoleCredits(organizationsWithCredits),
      staffs: organizationsWithCredits,
      organizationCredit: {
        remaining: orgQuotas.reduce((sum, q) => sum + (q.remaining || 0), 0),
        originalAmount: orgQuotas.reduce((sum, q) => sum + (q.originalAmount || 0), 0),
      },

      // System-specific fields
      type: 'system',
      totalOrganizations: organizations.length,
      systemSummary: {
        totalCreditsAllocated: orgQuotas.reduce((sum, q) => sum + (q.originalAmount || 0), 0),
        totalCreditsRemaining: orgQuotas.reduce((sum, q) => sum + (q.remaining || 0), 0),
        totalCreditsUsed: orgQuotas.reduce(
          (sum, q) => sum + ((q.originalAmount || 0) - (q.remaining || 0)),
          0,
        ),
        totalUsers: userQuotas.length,
        totalOrganizations: organizations.length,
        lowCreditCompaniesCount: lowCreditCompaniesCount,
      },

      // Sales/Super Admin specific fields
      lowCreditThreshold: 500,
    };

    return this.formatOutputData({ key: 'GET_COMPANY_CREDIT_MANAGEMENT' }, { data: payload });
  }

  private async getOrganizationCreditStatsData(orgId: string, user: any) {
    if (user.role.keyCode === RoleEnum.BASIC_USER && user.organizationId !== orgId) {
      throw new BadRequestException('Cannot view this company');
    }

    // Get ALL TOPUP organization credit information (exclude plan credits) and sum them
    const orgQuotas = await this.organizationQuotaRepository.find({
      where: { organizationId: orgId, featureId: 'credits', source: QuotaSource.TOPUP },
      select: ['remaining', 'originalAmount'],
    });

    // Sum all TOPUP credits from multiple topup transactions
    const orgQuota = orgQuotas.reduce(
      (total, credit) => ({
        remaining: total.remaining + (credit.remaining || 0),
        originalAmount: total.originalAmount + (credit.originalAmount || 0),
      }),
      { remaining: 0, originalAmount: 0 },
    );

    // Get total users in organization
    const totalUsers = await this.userRepository.count({
      where: { organizationId: orgId, isDeleted: false },
    });

    // Get total credits used by organization (from usage logs - only actual usage, not allocations)
    const totalCreditsUsed = await this.dataSource
      .getRepository('quota_usage_logs')
      .createQueryBuilder('qul')
      .select('SUM(qul.amount)', 'total')
      .where('qul.organizationId = :orgId', { orgId })
      .andWhere('qul.featureId = :featureId', { featureId: 'credits' })
      .andWhere('qul.logType = :logType', { logType: 'usage' })
      .andWhere('qul.quotaSource = :quotaSource', { quotaSource: QuotaSource.TOPUP })
      .getRawOne()
      .then((result) => parseFloat(result.total) || 0);

    const data = {
      // Common fields (consistent with credit management)
      creditManagementByRole: {}, // Empty for stats endpoint
      staffs: [], // Empty for stats endpoint
      organizationCredit: {
        remaining: orgQuota?.remaining || 0,
        originalAmount: orgQuota?.originalAmount || 0,
      },

      // Organization-specific fields
      type: 'organization',
      organizationId: orgId,
      organizationSummary: {
        totalCreditsAllocated: orgQuota?.originalAmount || 0,
        totalCreditsRemaining: orgQuota?.remaining || 0,
        totalCreditsUsed,
        totalUsers,
      },
    };

    return this.formatOutputData({ key: 'GET_CREDIT_STATS_SUCCESS' }, { data });
  }

  async topupCredits(orgId: string, userId: string, body: TopupCreditsDto) {
    try {
      const account = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true, organization: true },
      });

      if (account.role.keyCode !== RoleEnum.ADMIN && account.organizationId !== orgId) {
        throw new BadRequestException('Cannot topup credits for this organization');
      }

      // Validate packageId is required
      if (!body.packageId) {
        throw new BadRequestException('Package selection is required');
      }

      // Check for Enterprise package
      if (body.packageId === TopupPackageEnum.ENTERPRISE) {
        throw new BadRequestException(
          'Enterprise package requires custom pricing. Please contact sales.',
        );
      }

      // Get selected package
      const selectedPackage = getTopupPackage(body.packageId as any);
      if (!selectedPackage) {
        throw new BadRequestException('Invalid package selected');
      }

      const credits = selectedPackage.credits;
      const totalAmount = selectedPackage.price;
      const packageInfo = selectedPackage;

      // Get organization
      const organization = await this.dataSource.getRepository(OrganizationEntity).findOne({
        where: { id: orgId },
      });

      if (!organization) {
        throw new BadRequestException('Organization not found');
      }

      // Use injected payment service
      if (!this.paymentService) {
        throw new BadRequestException('Payment service not available');
      }

      // Create invoice for credit topup
      const description = packageInfo
        ? `${packageInfo.name} - ${credits} credits for ${organization.name}`
        : `Custom topup - ${credits} credits for ${organization.name}`;

      const paymentIntent = await this.paymentService.createPaymentIntentForCreditTopup({
        organizationId: orgId,
        amount: totalAmount,
        credits: credits,
        paymentMethodId: body.paymentMethodId,
        savePaymentMethod: body.savePaymentMethod || false, // Allow user to choose
        description: description,
        packageId: body.packageId,
      });
      console.log('🚀 ~ UserService ~ topupCredits ~ body.paymentMethodId:', body.paymentMethodId);

      // Determine payment status based on PaymentIntent status
      let paymentStatus = 'pending';
      let message =
        'Payment created successfully. Please confirm payment to complete the transaction.';

      if (paymentIntent.status === 'succeeded') {
        paymentStatus = 'completed';
        message = 'Payment completed successfully. Credits will be added automatically.';

        // If payment succeeded immediately, add credits now (webhook might be delayed)
        try {
          await this.addCreditsToOrganization(
            orgId,
            credits,
            paymentIntent.paymentIntentId,
            totalAmount,
            packageInfo.id,
          );
          message = 'Payment completed successfully. Credits have been added to your account.';
        } catch (error) {
          this.logger.warn(
            `Failed to add credits immediately after payment success: ${error.message}. Credits will be added via webhook.`,
          );
          message = 'Payment completed successfully. Credits will be added shortly via webhook.';
        }
      } else if (paymentIntent.status === 'requires_confirmation') {
        paymentStatus = 'requires_confirmation';
        message = 'Payment requires confirmation. Please confirm using the client secret.';
      } else if (paymentIntent.status === 'requires_payment_method') {
        paymentStatus = 'requires_payment_method';
        message = 'Payment requires a payment method. Please provide payment details.';
      } else if (paymentIntent.status === 'processing') {
        paymentStatus = 'processing';
        message = 'Payment is being processed. Credits will be added once payment is confirmed.';
      }

      const data = {
        paymentIntentId: paymentIntent.paymentIntentId,
        clientSecret: paymentIntent.clientSecret,
        invoiceId: paymentIntent.invoiceId,
        amount: totalAmount,
        credits: credits,
        status: paymentIntent.status,
        paymentStatus,
        message,
        packageInfo: {
          id: packageInfo.id,
          name: packageInfo.name,
          savings: packageInfo.savings,
          description: packageInfo.description,
        },
        pricePerCredit: totalAmount / credits,
      };

      return this.formatOutputData({ key: 'TOPUP_CREDITS_SUCCESS' }, { data });
    } catch (e) {
      console.log('🚀 ~ UserService ~ topupCredits ~ e:', e);
      return this.throwCommonMessage('TOPUP_CREDITS', e);
    }
  }

  async confirmTopupPayment(orgId: string, userId: string, paymentIntentId: string) {
    try {
      const account = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true, organization: true },
      });

      if (account.role.keyCode !== RoleEnum.ADMIN && account.organizationId !== orgId) {
        throw new BadRequestException('Cannot confirm topup payment for this organization');
      }

      // Use injected payment service to process the payment
      if (!this.paymentService) {
        throw new BadRequestException('Payment service not available');
      }

      // Retrieve the payment intent to check its status
      const paymentIntent = await this.paymentService.getPaymentIntentStatus(paymentIntentId);

      if (!paymentIntent) {
        throw new BadRequestException('Payment intent not found');
      }

      // Check if payment intent belongs to this organization
      if (paymentIntent.metadata?.organizationId !== orgId) {
        throw new BadRequestException('Payment intent does not belong to this organization');
      }

      // Check if payment is successful
      if (paymentIntent.status !== 'succeeded') {
        return this.formatOutputData(
          { key: 'CONFIRM_TOPUP_PAYMENT_PENDING' },
          {
            data: {
              status: paymentIntent.status,
              paymentIntentId,
              paymentStatus: 'pending',
              message: 'Payment is still processing or failed',
            },
          },
        );
      }

      // Payment successful - check if credits were already added via webhook
      const credits = parseInt(paymentIntent.metadata?.credits || '0');
      const packageId = paymentIntent.metadata?.packageId;
      const amount = parseFloat(paymentIntent.metadata?.amount || '0');

      if (!credits || credits <= 0) {
        throw new BadRequestException('Invalid credit amount in payment intent');
      }

      // Check if credits were already added by webhook (idempotent check)
      const existingQuota = await this.organizationQuotaRepository.findOne({
        where: {
          organizationId: orgId,
          featureId: 'credits',
          source: QuotaSource.TOPUP,
          sourceId: paymentIntentId,
        },
      });

      if (!existingQuota) {
        // Credits not yet added - add them now
        await this.addCreditsToOrganization(orgId, credits, paymentIntentId, amount, packageId);
      } else {
        this.logger.log(
          `Credits for PaymentIntent ${paymentIntentId} already processed via webhook. Skipping duplicate processing.`,
        );
      }

      const data = {
        paymentIntentId,
        status: paymentIntent.status,
        paymentStatus: 'completed',
        credits,
        amount,
        packageId,
        message: existingQuota
          ? 'Credits already processed successfully'
          : 'Credits added successfully',
      };

      return this.formatOutputData({ key: 'CONFIRM_TOPUP_PAYMENT_SUCCESS' }, { data });
    } catch (e) {
      console.log('🚀 ~ UserService ~ confirmTopupPayment ~ e:', e);
      return this.throwCommonMessage('CONFIRM_TOPUP_PAYMENT', e);
    }
  }

  private async addCreditsToOrganization(
    organizationId: string,
    credits: number,
    sourceId: string,
    amount: number,
    packageId?: string,
  ) {
    // Check if this topup transaction already exists (idempotent check)
    const existingTopup = await this.organizationQuotaRepository.findOne({
      where: {
        organizationId,
        featureId: 'credits',
        source: QuotaSource.TOPUP,
        sourceId,
      },
    });

    if (existingTopup) {
      this.logger.log(
        `Credits for sourceId ${sourceId} already added to organization ${organizationId}. Skipping duplicate.`,
      );
      return;
    }

    // Create a new record for this topup transaction
    await this.organizationQuotaRepository.save({
      organizationId,
      featureId: 'credits',
      originalAmount: credits,
      remaining: credits,
      source: QuotaSource.TOPUP,
      sourceId,
      unit: FeatureUnitEnum.CREDIT,
    });

    // Log the credit addition
    await this.dataSource.getRepository(QuotaUsageLogEntity).save({
      userId: null, // Organization-level credit addition
      organizationId,
      featureId: 'credits',
      unit: FeatureUnitEnum.CREDIT,
      amount: credits,
      purpose: `Credit topup - ${packageId || 'custom'} package`,
      usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
      sourceQuotaId: sourceId, // Use the payment intent ID as source
      quotaSource: QuotaSource.TOPUP,
      performedBy: null,
      performedByType: PerformedByType.SYSTEM,
      logType: QuotaLogType.ALLOCATION, // Adding credits
      isRestoration: false,
      restoredByLogId: null,
      originalUsageLogId: null,
    });

    this.logger.log(
      `Added ${credits} credits to organization ${organizationId} from payment ${sourceId}`,
    );
  }

  async getTopupHistory(orgId: string, userId: string) {
    try {
      const account = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true },
      });

      if (account.role.keyCode !== RoleEnum.ADMIN && account.organizationId !== orgId) {
        throw new BadRequestException('Cannot view topup history for this organization');
      }

      const topupRecords = await this.organizationQuotaRepository.find({
        where: {
          organizationId: orgId,
          featureId: 'credits',
          source: QuotaSource.TOPUP,
        },
        order: { id: 'DESC' },
      });

      const topupHistory = await Promise.all(
        topupRecords.map(async (record) => {
          let invoiceDetails = null;

          if (this.paymentService && record.sourceId) {
            try {
              // Try to get invoice details from Stripe
              if (record.sourceId.startsWith('in_')) {
                // It's an invoice ID
                invoiceDetails = await this.paymentService.getInvoiceDetails(record.sourceId);
              }
            } catch (error) {
              this.logger.warn(
                `Could not fetch invoice details for ${record.sourceId}: ${error.message}`,
              );
            }
          }

          // Determine package info from credits amount (since we only allow packages now)
          let packageInfo = null;
          const packages = getAllTopupPackages();

          // Find matching package based on credits amount
          const matchingPackage = packages.find((pkg) => pkg.credits === record.originalAmount);
          if (matchingPackage) {
            packageInfo = {
              id: matchingPackage.id,
              name: matchingPackage.name,
              savings: matchingPackage.savings,
            };
          }

          return {
            id: record.id,
            date: new Date(), // Use current date as fallback since createdAt might not exist
            credits: record.originalAmount,
            amount: invoiceDetails?.amountPaid || record.originalAmount, // Use actual paid amount from invoice
            currency: 'USD',
            sourceId: record.sourceId,
            status: 'completed',
            packageInfo,
            pricePerCredit: matchingPackage
              ? matchingPackage.price / matchingPackage.credits
              : invoiceDetails?.amountPaid
                ? invoiceDetails.amountPaid / record.originalAmount
                : 1,
            invoiceDetails,
          };
        }),
      );

      const data = {
        topupHistory,
        totalTopups: topupHistory.length,
        totalCreditsTopup: topupRecords.reduce((sum, record) => sum + record.originalAmount, 0),
        totalAmountSpent: topupRecords.reduce((sum, record) => sum + record.originalAmount, 0), // Assuming $1 per credit
      };

      return this.formatOutputData({ key: 'GET_TOPUP_HISTORY_SUCCESS' }, { data });
    } catch (e) {
      return this.throwCommonMessage('GET_TOPUP_HISTORY', e);
    }
  }

  async getTopupPackages(userId: string) {
    try {
      const profile = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true },
      });

      if (!profile) {
        throw new BadRequestException('Profile not found');
      }

      // Only admins can view topup packages
      if (
        profile.role.keyCode !== RoleEnum.ADMIN &&
        profile.role.keyCode !== RoleEnum.SUPER_ADMIN
      ) {
        throw new BadRequestException('Cannot view topup packages');
      }

      const packages = getAllTopupPackages().map((pkg) => ({
        ...pkg,
        savings: calculateSavings(pkg.id),
        pricePerCredit: pkg.credits > 0 ? pkg.price / pkg.credits : 0,
        regularPrice: pkg.credits > 0 ? pkg.credits * 1 : 0, // $1 per credit regular price
        totalSavings: pkg.credits > 0 ? pkg.credits * 1 - pkg.price : 0,
      }));

      const data = {
        packages,
        currency: 'USD',
        note: 'Only predefined packages are available. Contact sales for Enterprise pricing.',
      };

      return this.formatOutputData({ key: 'GET_TOPUP_PACKAGES_SUCCESS' }, { data });
    } catch (e) {
      return this.throwCommonMessage('GET_TOPUP_PACKAGES', e);
    }
  }

  async checkLimitCredit(orgId: string, incomingCredit: number) {
    const listUsers = await this.userRepository.find({
      where: { organizationId: orgId },
      relations: { role: true },
    });
    const existingOrganization = await this.dataSource
      .getRepository(OrganizationQuotaEntity)
      .findOne({ where: { organizationId: orgId, featureId: 'credits' } });

    if (!existingOrganization) {
      return false;
    }

    const listUserIds = listUsers.map((item) => item.id);
    const listUserLimit = await this.userQuotaRepository.find({
      where: {
        userId: In(listUserIds),
      },
    });

    const { totalBalance, totalUsed } = listUserLimit
      .filter((item) => listUserIds.includes(item.userId))
      .reduce(
        (acc, item) => ({
          totalBalance: item.originalAmount,
          totalUsed: 0,
        }),
        { totalBalance: 0, totalUsed: 0 },
      );

    return Number(existingOrganization.originalAmount) - totalBalance - incomingCredit > 0;
  }

  async upsertCreditManagement(orgId: string, userId: string, body: UpsertCreditManagementDto) {
    try {
      const account = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true, organization: true },
      });
      if (
        account.role.keyCode === RoleEnum.BASIC_USER ||
        account.role.keyCode === RoleEnum.MANAGEMENT
      ) {
        throw new BadRequestException('Cannot view this company');
      }

      const userLimit = await this.userQuotaRepository.findOne({
        where: {
          userId: body.userId,
        },
      });

      const incomingCredit = userLimit
        ? body.totalCredit - (userLimit.remaining || 0)
        : body.totalCredit;
      const checkLimitCreditResult = await this.checkLimitCredit(orgId, incomingCredit);
      if (!checkLimitCreditResult) {
        throw new BadRequestException('Credit not enough');
      }

      const changeCredit = body.totalCredit - (userLimit?.remaining || 0);

      const existingOrganization = await this.dataSource
        .getRepository(OrganizationQuotaEntity)
        .findOne({ where: { organizationId: orgId, featureId: 'credits' } });

      if (!userLimit) {
        const record: any = {
          userId: body.userId,
          organizationId: existingOrganization.organizationId,
          featureId: 'credits',
          unit: UserLimitType.CREDIT,
          remaining: body.totalCredit,
          originalAmount: body.totalCredit,
          sourceId: existingOrganization.sourceId,
        };

        const creditChange = {
          ...existingOrganization,
          remaining: existingOrganization.remaining - changeCredit,
        };

        await this.organizationQuotaRepository.update(existingOrganization.id, creditChange);
        await this.userQuotaRepository.insert(record);
      } else {
        const payload = {
          ...userLimit,
          originalAmount: userLimit.originalAmount + changeCredit,
          remaining: changeCredit > 0 ? userLimit.remaining + changeCredit : body.totalCredit,
        };
        const creditChange = {
          ...existingOrganization,
          remaining: existingOrganization.remaining - changeCredit,
        };
        await this.userQuotaRepository.update(userLimit.id, payload);
        await this.organizationQuotaRepository.update(existingOrganization.id, creditChange);
      }

      return this.formatOutputData({ key: 'UPDATE_CREDIT_MANAGEMENT' }, { data: {} });
    } catch (e) {
      console.log('UPDATE_CREDIT_MANAGEMENT', e);
      return this.throwCommonMessage('UPDATE_CREDIT_MANAGEMENT', e);
    }
  }

  async getCreditManagementOrganizations(userId: string) {
    const profile = await this.userRepository.findOne({
      where: { id: userId },
      relations: { role: true },
    });
    if (!profile) {
      throw new BadRequestException('Profile not found');
    }

    if (profile.role.keyCode !== RoleEnum.SUPER_ADMIN && profile.role.keyCode !== RoleEnum.ADMIN) {
      throw new BadRequestException('Can not view this API');
    }

    const query = this.dataSource
      .createQueryBuilder(OrganizationEntity, 'organization')
      .leftJoinAndSelect('organization.users', 'user')
      .leftJoinAndSelect('user.role', 'role')
      .select([
        'organization.id',
        'organization.name',
        'user.id',
        'user.username',
        'user.fullName',
        'user.email',
        'user.avatarId',
        'role.name',
        'organization.bhClientId',
        'organization.bhClientSecret',
        'organization.bhUsername',
        'organization.bhPassword',
        'organization.bhToken',
        'organization.companySize',
        'organization.companyOwner',
        'organization.companyWebsite',
        'organization.companyIndustries',
        'organization.companyTypes',
        'organization.companyAvatar',
        'organization.address',
        'organization.phone',
        'organization.tags',
        'organization.license',
        'organization.companyEmail',
        'organization.companyAdmins',
        'organization.status',
        'organization.createdBy',
      ]);

    if (profile.role.keyCode === RoleEnum.ADMIN) {
      query.andWhere('organization.id = :orgId', { orgId: profile.organizationId });
    }

    const organizations = await query.orderBy('organization.status', 'DESC').getMany();

    // Enhance organizations with credit information
    const enhancedOrganizations = await Promise.all(
      organizations.map(async (org) => {
        // Get organization credit information (sum all credit sources)
        const orgQuotas = await this.organizationQuotaRepository.find({
          where: { organizationId: org.id, featureId: 'credits', source: QuotaSource.TOPUP },
          select: ['originalAmount', 'remaining'],
        });

        const totalCredits = orgQuotas.reduce((sum, quota) => sum + (quota.originalAmount || 0), 0);
        const remainingCredits = orgQuotas.reduce((sum, quota) => sum + (quota.remaining || 0), 0);
        const usedCredits = totalCredits - remainingCredits;

        return {
          ...org,
          totalCredits,
          usedCredits,
        };
      }),
    );

    return this.formatOutputData({ key: 'GET_CREDIT_MANAGEMENT' }, { data: enhancedOrganizations });
  }

  async handleCreateDocument(
    { fileId, note }: CreateDocumentFileDto,
    userId: string,
    organizationId: string,
  ) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }
      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);
      const organization = await organizationRepository.findOneBy({ id: organizationId });
      if (!organization) {
        throw new NotFoundException('Organization not found');
      }
      const currentDocuments = organization.documents || [];
      const newDocument = { fileId, note, createdBy: userId, createdAt: new Date() };
      const updatedDocuments = [...currentDocuments, newDocument];

      await organizationRepository.update(organizationId, { documents: updatedDocuments });

      return this.formatOutputData({ key: 'CREATE_FILE_SUCCESS' }, { data: [...updatedDocuments] });
    } catch (error) {
      console.error('Error in handleCreateDocument:', error);
      return this.throwCommonMessage(
        'CREATE_FILE_FAILED',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async handleDeleteDocument(fileId: string, userId: string, organizationId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const organizationRepository = this.dataSource.getRepository(OrganizationEntity);
      const organization = await organizationRepository.findOneBy({ id: organizationId });
      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      const currentDocuments = organization.documents || [];
      const updatedDocuments = currentDocuments.filter((doc) => doc.fileId !== fileId);
      // Delete the file from S3
      await this.fileUploadService.bulkDeleteFiles([fileId]);
      // Delete the file from Organization
      await organizationRepository.update(organizationId, { documents: updatedDocuments });

      return this.formatOutputData({ key: 'DELETE_FILE_SUCCESS' }, { data: [...updatedDocuments] });
    } catch (error) {
      return this.throwCommonMessage(
        'DELETE_FILE_FAILED',
        new InternalServerErrorException('Something went wrong'),
      );
    }
  }

  async completeOnboarding(userId: string, completeOnboardingDto: CompleteOnboardingDto) {
    try {
      const { newPassword, profileInfo } = completeOnboardingDto;

      // Find the user
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: { role: true },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if the user has already completed onboarding
      if (!user.initialPassword) {
        return this.formatOutputData(
          { key: 'COMPLETE_ONBOARDING_SUCCESS' },
          {
            data: {
              alreadyOnboarded: true,
            },
          },
        );
      }

      // Hash the new password
      const { hashPassword } = await this.hashPassword(newPassword);

      // Update user data
      const updateData: any = {
        password: hashPassword,
        initialPassword: null, // Clear the initial password
      };

      // Add optional profile information if provided
      if (profileInfo) {
        if (profileInfo.fullName) updateData.fullName = profileInfo.fullName;
        if (profileInfo.jobTitle) updateData.jobTitle = profileInfo.jobTitle;
        if (profileInfo.timezone) updateData.timezone = profileInfo.timezone;
        if (profileInfo.linkedinUrl) updateData.linkedinUrl = profileInfo.linkedinUrl;
      }

      // Update the user
      await this.userRepository.update(userId, updateData);

      // If user is an admin, also complete admin onboarding
      if (user.role.keyCode === RoleEnum.ADMIN) {
        const { organizationId } = user;
        const organizationRepository = this.dataSource.getRepository(OrganizationEntity);
        const organization = await organizationRepository.findOneBy({ id: organizationId });

        if (organization && !organization.adminOnboardingCompleted) {
          await organizationRepository.update(organizationId, { adminOnboardingCompleted: true });

          // Activate the subscription
          const subscription =
            await this.subscriptionService.getSubscriptionForOrganization(organizationId);
          if (subscription && subscription.status === SubscriptionStatus.PENDING) {
            await this.dataSource
              .createQueryBuilder()
              .update(SubscriptionEntity)
              .set({ status: SubscriptionStatus.ACTIVE })
              .where('id = :subscriptionId', { subscriptionId: subscription.id })
              .execute();

            this.logger.log(
              `Activated subscription ${subscription.id} for company ${organizationId} after admin onboarding completion`,
            );
          }

          // Get all pending users in the organization
          const pendingUsers = await this.userRepository.find({
            where: {
              organizationId,
              status: UserStatus.PENDING,
            },
          });

          if (pendingUsers.length > 0) {
            // Assign licenses to pending users and activate them
            await this.assignLicensesToPendingUsers(organizationId, pendingUsers, userId);

            // Send onboarding emails to activated users
            const emailPromises = pendingUsers.map((activatedUser) =>
              this.mailService.bulkSendOnboardingEmails([
                { toEmail: activatedUser.email, password: activatedUser.initialPassword || '' },
              ]),
            );

            await Promise.all(emailPromises);

            return this.formatOutputData(
              { key: 'COMPLETE_ONBOARDING_SUCCESS' },
              {
                data: {
                  isAdmin: true,
                  adminOnboardingCompleted: true,
                  activatedUsers: pendingUsers.length,
                },
              },
            );
          }
        }
      }

      return this.formatOutputData(
        { key: 'COMPLETE_ONBOARDING_SUCCESS' },
        {
          data: {
            isAdmin: user.role.keyCode === RoleEnum.ADMIN,
            adminOnboardingCompleted: user.role.keyCode === RoleEnum.ADMIN,
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('COMPLETE_ONBOARDING_FAILED', error);
    }
  }

  async quickCreateUser(userId: string, quickCreateUserDto: QuickCreateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: { role: true },
    });

    if (!user || user.role.keyCode !== RoleEnum.SUPER_ADMIN) {
      throw new BadRequestException('Only super admin can quick-create new user');
    }

    const organizationId =
      (quickCreateUserDto.companyId ?? quickCreateUserDto.companyName)
        ? ((
            await this.dataSource
              .getRepository(OrganizationEntity)
              .findOneBy({ name: quickCreateUserDto.companyName })
          )?.id ??
          (await this.dataSource
            .createQueryBuilder(OrganizationEntity, 'o')
            .insert()
            .values({
              name: quickCreateUserDto.companyName || uuid(),
              status: OrganizationStatusEnum.APPROVED,
              createdBy: userId,
            })
            .returning('id')
            .execute()
            .then((result) => result.raw[0].id)))
        : null;

    const roleId =
      quickCreateUserDto.roleId ||
      (await this.roleRepository.findOneBy({ keyCode: quickCreateUserDto.roleName }))?.id;

    if (!roleId || !organizationId) {
      throw new BadRequestException('Role or organization not found');
    }

    const newUser: Partial<UserEntity> = {
      email: quickCreateUserDto.email,
      password: (await this.hashPassword(quickCreateUserDto.password)).hashPassword,
      username: quickCreateUserDto.username,
      roleId,
      organizationId,
      status: UserStatus.ACTIVE, // Default to ACTIVE for quick create
      initialPassword: quickCreateUserDto.password,
    };

    console.log(newUser, 'newUser');

    const createdUser = await this.userRepository.save(newUser);
    const permissions = await this.dataSource.createQueryBuilder(PermissionEntity, 'p').getMany();

    await this.dataSource
      .createQueryBuilder(UserPermissionEntity, 'up')
      .insert()
      .values(
        permissions.map((item) => ({
          userId: createdUser.id,
          permissionId: item.id,
          allowRead: true,
          allowWrite: true,
        })),
      )
      .execute();

    return this.formatOutputData({ key: 'QUICK_CREATE_USER_SUCCESS' }, { data: createdUser });
  }

  async getContactAdded(userId: string, query: GetDashboardMetricsQuery) {
    const { toDate, fromDate } = query;
    const whereCondition = [];
    if (userId) {
      whereCondition.push(['(created_by = :userId)', { userId }]);
    }

    if (fromDate) {
      whereCondition.push(['(created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereCondition.push(['(created_at <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.dataSource.createQueryBuilder(FoundContactEntity, 'sed');

    const addedFromListBuilder = this.dataSource.createQueryBuilder(ContactEntity, 'sed');

    whereCondition.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
        addedFromListBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
        addedFromListBuilder.andWhere(condition, variables);
      }
    });

    const dataAddedFromBH = await queryBuilder.getMany();
    const dataAddedFromList = await addedFromListBuilder.getMany();
    return this.formatOutputData(
      { key: 'STATS_CONTACT_ADDED' },
      {
        data: {
          dataAddedFromBH,
          dataAddedFromList,
        },
      },
    );
  }

  async getCompanyAdded(userId: string, query: GetDashboardMetricsQuery) {
    const { toDate, fromDate } = query;
    const whereCondition = [];
    const whereCrmCondition = [];
    if (userId) {
      whereCondition.push(['(created_by = :userId)', { userId }]);
      whereCrmCondition.push(['(creator = :userId)', { userId }]);
    }

    if (fromDate) {
      whereCondition.push(['(created_at >= :fromDate)', { fromDate }]);
      whereCrmCondition.push(['(created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereCondition.push(['(created_at <= :toDate)', { toDate }]);
      whereCrmCondition.push(['(created_at <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.dataSource.createQueryBuilder(FoundCompanyEntity, 'sed');

    const addedFromListBuilder = this.dataSource.createQueryBuilder(CrmCompanyEntity, 'sed');

    whereCondition.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    whereCrmCondition.forEach(([condition, variables], i) => {
      if (i === 0) {
        addedFromListBuilder.where(condition, variables);
      } else {
        addedFromListBuilder.andWhere(condition, variables);
      }
    });

    const dataAddedFromBH = await queryBuilder.getMany();
    const dataAddedFromCRM = await addedFromListBuilder.getMany();
    return this.formatOutputData(
      { key: 'STATS_COMPANY_ADDED' },
      {
        data: {
          dataAddedFromBH,
          dataAddedFromCRM,
        },
      },
    );
  }
}
