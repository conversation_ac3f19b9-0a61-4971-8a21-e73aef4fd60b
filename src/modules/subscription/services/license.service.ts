import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { LicenseEntity, LicenseStatus } from '../entities/license.entity';
import { SubscriptionEntity, SubscriptionStatus } from '../entities/subscription.entity';
import { UserEntity } from '../../user/entities/user.entity';

import { LicenseTransferEntity } from '../entities/license-transfer.entity';
import { SUBSCRIPTION_SYSTEM_CONFIG } from '../../../configs/subscription-system.config';
import { QuotaService } from './quota.service';
import { PLANS } from '../../../configs/plans.config';
import { PLAN_FEATURES } from '../../../configs/plan-features.config';
import { QuotaResetInterval, QuotaSource } from '../entities/organization-quota.entity';

@Injectable()
export class LicenseService {
  private readonly logger = new Logger(LicenseService.name);

  constructor(
    @InjectRepository(LicenseEntity)
    private readonly licenseRepository: Repository<LicenseEntity>,
    @InjectRepository(LicenseTransferEntity)
    private readonly licenseTransferRepository: Repository<LicenseTransferEntity>,
    private readonly dataSource: DataSource,
    private readonly quotaService: QuotaService,
  ) {}

  /**
   * Execute transaction with custom timeout for license operations
   */
  private async executeTransactionWithTimeout<T>(
    operation: (entityManager: any) => Promise<T>,
    timeoutSeconds: number = SUBSCRIPTION_SYSTEM_CONFIG.LICENSE_ALLOCATION_TIMEOUT_SECONDS,
  ): Promise<T> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Set statement timeout for this transaction
      await queryRunner.query(`SET SESSION idle_in_transaction_session_timeout = '3min'`);
      await queryRunner.query(`SET SESSION statement_timeout = '${timeoutSeconds}s'`);

      const result = await operation(queryRunner.manager);
      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Create licenses for a subscription
   */
  async createLicensesForSubscription(
    subscriptionId: string,
    licenseCount: number,
    entityManager?: any,
  ): Promise<LicenseEntity[]> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(LicenseEntity)
        : this.licenseRepository;

      const licenses: LicenseEntity[] = [];
      for (let i = 0; i < licenseCount; i++) {
        const license = repository.create({
          subscriptionId,
          userId: null, // Initially unassigned
          status: LicenseStatus.ACTIVE,
          assignedAt: null,
        });
        licenses.push(license);
      }

      const savedLicenses = await repository.save(licenses);
      this.logger.log(`Created ${licenseCount} licenses for subscription ${subscriptionId}`);

      return savedLicenses;
    } catch (error) {
      this.logger.error(`Error creating licenses: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get subscription for preview (no license creation)
   */
  async getSubscriptionForPreview(
    subscriptionId: string,
    organizationId: string,
  ): Promise<SubscriptionEntity> {
    try {
      const subscription = await this.dataSource.getRepository(SubscriptionEntity).findOne({
        where: { id: subscriptionId, organizationId },
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription ${subscriptionId} not found or not accessible`);
      }

      return subscription;
    } catch (error) {
      this.logger.error(`Error getting subscription for preview: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add additional licenses to existing subscription
   */
  async addLicensesToSubscription(
    subscriptionId: string,
    additionalLicenseCount: number,
    organizationId: string,
    performedBy?: string,
    entityManager?: any,
  ): Promise<{
    newLicenses: LicenseEntity[];
    totalLicenseCount: number;
    subscription: SubscriptionEntity;
    needsNewPaymentIntent: boolean;
    paymentIntentCanceled: boolean;
    oldPaymentIntentId: string | null;
  }> {
    // If no entityManager provided, wrap in transaction with extended timeout
    if (!entityManager) {
      return await this.executeTransactionWithTimeout(async (transactionManager) => {
        return this.addLicensesToSubscription(
          subscriptionId,
          additionalLicenseCount,
          organizationId,
          performedBy,
          transactionManager,
        );
      }, SUBSCRIPTION_SYSTEM_CONFIG.LICENSE_ALLOCATION_TIMEOUT_SECONDS);
    }

    try {
      const subscriptionRepo = entityManager
        ? entityManager.getRepository(SubscriptionEntity)
        : this.dataSource.getRepository(SubscriptionEntity);

      // Get subscription and validate organization access
      const subscription = await subscriptionRepo.findOne({
        where: { id: subscriptionId, organizationId },
      });
      console.log("🚀 ~ LicenseService ~ subscription:", subscription)

      if (!subscription) {
        throw new NotFoundException(`Subscription ${subscriptionId} not found or not accessible`);
      }

      if (
        subscription.status !== SubscriptionStatus.ACTIVE &&
        subscription.status !== SubscriptionStatus.PENDING
      ) {
        throw new BadRequestException(
          `Cannot add licenses to subscription with status: ${subscription.status}`,
        );
      }

      // Calculate expected total license count after addition
      const expectedTotalCount = subscription.licenseCount + additionalLicenseCount;
      console.log(
        '🚀 ~ LicenseService ~ expectedTotalCount:',
        expectedTotalCount,
        additionalLicenseCount,
      );

      // Check current license count from database (idempotency check)
      const licenseRepo = entityManager
        ? entityManager.getRepository(LicenseEntity)
        : this.licenseRepository;

      const currentLicenseCount = await licenseRepo.count({
        where: { subscriptionId },
      });

      if (currentLicenseCount >= expectedTotalCount) {
        this.logger.log(
          `Licenses already exist for subscription ${subscriptionId}. Current: ${currentLicenseCount}, Expected: ${expectedTotalCount}. Skipping creation.`,
        );

        // Get existing licenses for response
        const existingLicenses = await licenseRepo.find({
          where: { subscriptionId },
          order: { createdAt: 'DESC' },
          take: additionalLicenseCount,
        });

        return {
          newLicenses: existingLicenses,
          totalLicenseCount: currentLicenseCount,
          subscription,
          needsNewPaymentIntent: subscription.status === SubscriptionStatus.PENDING,
          paymentIntentCanceled: false, // No cancellation needed for idempotent case
          oldPaymentIntentId: null, // No old PaymentIntent to cancel
        };
      }

      // Calculate how many licenses we actually need to create
      const licensesToCreate = expectedTotalCount - currentLicenseCount;
      const actualLicensesToCreate = Math.min(licensesToCreate, additionalLicenseCount);

      this.logger.log(
        `Creating ${actualLicensesToCreate} licenses for subscription ${subscriptionId} (requested: ${additionalLicenseCount}, current: ${currentLicenseCount}, expected total: ${expectedTotalCount})`,
      );

      // Create additional licenses
      const newLicenses = await this.createLicensesForSubscription(
        subscriptionId,
        actualLicensesToCreate,
        entityManager,
      );

      // Update subscription license count to match expected total
      const oldLicenseCount = subscription.licenseCount;
      subscription.licenseCount = expectedTotalCount;

      this.logger.log(
        `Updating subscription ${subscriptionId} license count: ${oldLicenseCount} → ${expectedTotalCount}`,
      );

      // If subscription is PENDING and has existing PaymentIntent, mark it for cancellation
      let paymentIntentCanceled = false;
      let oldPaymentIntentId = null;
      if (subscription.status === SubscriptionStatus.PENDING && subscription.stripeSubscriptionId) {
        this.logger.log(
          `Subscription ${subscriptionId} is PENDING with existing PaymentIntent ${subscription.stripeSubscriptionId}. Marking old PaymentIntent for cancellation due to license count change.`,
        );

        // Store the old PaymentIntent ID for controller to cancel
        oldPaymentIntentId = subscription.stripeSubscriptionId;
        paymentIntentCanceled = true; // Will be canceled by controller

        // Clear the old PaymentIntent ID to indicate it's no longer valid
        subscription.stripeSubscriptionId = null;
      }

      const updatedSubscription = await subscriptionRepo.save(subscription);

      this.logger.log(
        `Successfully updated subscription ${subscriptionId}: licenseCount=${updatedSubscription.licenseCount}, added ${actualLicensesToCreate} licenses. Total: ${expectedTotalCount}`,
      );

      // Verify the update was successful
      const verifySubscription = await subscriptionRepo.findOne({
        where: { id: subscriptionId },
      });

      if (verifySubscription && verifySubscription.licenseCount !== expectedTotalCount) {
        this.logger.error(
          `Subscription license count verification failed! Expected: ${expectedTotalCount}, Actual: ${verifySubscription.licenseCount}`,
        );
      } else {
        this.logger.log(
          `Subscription license count verification successful: ${verifySubscription?.licenseCount}`,
        );
      }

      return {
        newLicenses,
        totalLicenseCount: expectedTotalCount,
        subscription: updatedSubscription,
        needsNewPaymentIntent: subscription.status === SubscriptionStatus.PENDING,
        paymentIntentCanceled,
        oldPaymentIntentId,
      };
    } catch (error) {
      this.logger.error(`Error adding licenses to subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Assign license to user
   */
  async assignLicenseToUser(
    licenseId: string,
    userId: string,
    performedBy?: string,
    entityManager?: any,
  ): Promise<LicenseEntity> {
    try {
      const licenseRepo = entityManager
        ? entityManager.getRepository(LicenseEntity)
        : this.licenseRepository;
      const userRepo = entityManager
        ? entityManager.getRepository(UserEntity)
        : this.dataSource.getRepository(UserEntity);

      const license = await licenseRepo
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .where('license.id = :licenseId', { licenseId })
        .getOne();

      if (!license) {
        throw new NotFoundException(`License ${licenseId} not found`);
      }

      if (license.userId) {
        throw new BadRequestException(
          `License ${licenseId} is already assigned to user ${license.userId}`,
        );
      }

      const user = await userRepo.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User ${userId} not found`);
      }

      // Check if user already has a license for this subscription
      const existingLicense = await licenseRepo
        .createQueryBuilder('license')
        .where('license.subscriptionId = :subscriptionId', {
          subscriptionId: license.subscriptionId,
        })
        .andWhere('license.userId = :userId', { userId })
        .andWhere('license.status = :status', { status: LicenseStatus.ACTIVE })
        .getOne();

      if (existingLicense) {
        throw new BadRequestException(
          `User ${userId} already has an active license for this subscription`,
        );
      }

      // Assign license
      license.userId = userId;
      license.assignedAt = new Date();
      license.status = LicenseStatus.ACTIVE;

      const savedLicense = await licenseRepo.save(license);

      // Allocate credits to user based on plan
      await this.allocateCreditsToLicense(license, entityManager);

      this.logger.log(`Assigned license ${licenseId} to user ${userId}`);
      return savedLicense;
    } catch (error) {
      this.logger.error(`Error assigning license: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Transfer license from one user to another
   */
  async transferLicense(
    licenseId: string,
    toUserId: string,
    transferReason?: string,
    performedBy?: string,
    entityManager?: any,
  ): Promise<LicenseEntity> {
    try {
      const licenseRepo = entityManager
        ? entityManager.getRepository(LicenseEntity)
        : this.licenseRepository;
      const transferRepo = entityManager
        ? entityManager.getRepository(LicenseTransferEntity)
        : this.licenseTransferRepository;

      const license = await licenseRepo
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .leftJoinAndSelect('license.user', 'user')
        .where('license.id = :licenseId', { licenseId })
        .getOne();

      if (!license) {
        throw new NotFoundException(`License ${licenseId} not found`);
      }

      const fromUserId = license.userId;
      if (!fromUserId) {
        throw new BadRequestException(`License ${licenseId} is not currently assigned`);
      }

      if (fromUserId === toUserId) {
        throw new BadRequestException(`License is already assigned to user ${toUserId}`);
      }

      // Check transfer limits
      await this.validateTransferLimits(fromUserId, entityManager);

      // Get current credit balances before transfer
      const creditsTransferred = await this.getCreditBalancesForLicense(licenseId, entityManager);

      // Create transfer record
      const transfer = transferRepo.create({
        licenseId,
        fromUserId,
        toUserId,
        transferReason,
        creditsTransferred,
        performedBy,
      });

      await transferRepo.save(transfer);

      // Update license assignment
      license.userId = toUserId;
      license.assignedAt = new Date();
      license.status = LicenseStatus.ACTIVE;

      const savedLicense = await licenseRepo.save(license);

      // Transfer quotas from old user to new user
      await this.transferUserQuotas(fromUserId, toUserId, licenseId, entityManager);

      this.logger.log(
        `Transferred license ${licenseId} from user ${fromUserId} to user ${toUserId}`,
      );
      return savedLicense;
    } catch (error) {
      this.logger.error(`Error transferring license: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available licenses for a subscription
   */
  async getAvailableLicenses(subscriptionId: string): Promise<LicenseEntity[]> {
    return this.licenseRepository.find({
      where: {
        subscriptionId,
        userId: null,
        status: LicenseStatus.ACTIVE,
      },
    });
  }

  /**
   * Get licenses for a user
   */
  async getUserLicenses(userId: string): Promise<LicenseEntity[]> {
    return this.licenseRepository.find({
      where: {
        userId,
        status: LicenseStatus.ACTIVE,
      },
      relations: ['subscription'],
    });
  }

  /**
   * Allocate credits to license based on plan
   */
  private async allocateCreditsToLicense(
    license: LicenseEntity,
    entityManager?: any,
  ): Promise<void> {
    try {
      if (!license.userId) {
        return; // No user assigned, skip credit allocation
      }

      const subscription = license.subscription;
      const plan = PLANS[subscription.planId];
      const planFeatures = PLAN_FEATURES[subscription.planId];

      if (!plan || !planFeatures) {
        this.logger.warn(`Plan ${subscription.planId} not found, skipping credit allocation`);
        return;
      }

      // Allocate credits feature
      const creditsFeature = planFeatures.credits;
      if (creditsFeature && creditsFeature.amount) {
        await this.quotaService.allocateQuotaToUser(
          license.userId,
          subscription.organizationId,
          'credits',
          creditsFeature.amount,
          'credit',
          creditsFeature.resetInterval || QuotaResetInterval.SUBSCRIPTION,
          QuotaSource.SUBSCRIPTION,
          subscription.id,
          entityManager,
        );

        // Credits allocated via quota system above
      }

      this.logger.log(`Allocated credits to license ${license.id} for user ${license.userId}`);
    } catch (error) {
      this.logger.error(`Error allocating credits to license: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get licenses for organization with filtering and pagination
   */
  async getLicensesForOrganization(
    organizationId: string,
    query: {
      status?: string;
      userId?: string;
      page?: number;
      limit?: number;
    } = {},
  ): Promise<{
    licenses: LicenseEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const { status, userId, page = 1, limit = 10 } = query;
      const skip = (page - 1) * limit;

      const queryBuilder = this.licenseRepository
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .leftJoinAndSelect('license.user', 'user')
        .where('subscription.organizationId = :organizationId', { organizationId });

      if (status) {
        queryBuilder.andWhere('license.status = :status', { status });
      }

      if (userId) {
        queryBuilder.andWhere('license.userId = :userId', { userId });
      }

      const [licenses, total] = await queryBuilder
        .orderBy('license.createdAt', 'DESC')
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        licenses,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error getting licenses for organization: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get license by ID with organization validation
   */
  async getLicenseById(licenseId: string, organizationId: string): Promise<LicenseEntity> {
    try {
      const license = await this.licenseRepository
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .leftJoinAndSelect('license.user', 'user')
        .leftJoinAndSelect('license.quotas', 'quotas')
        .where('license.id = :licenseId', { licenseId })
        .andWhere('subscription.organizationId = :organizationId', { organizationId })
        .getOne();

      if (!license) {
        throw new NotFoundException(`License ${licenseId} not found or not accessible`);
      }

      return license;
    } catch (error) {
      this.logger.error(`Error getting license by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Unassign license from user
   */
  async unassignLicense(
    licenseId: string,
    organizationId: string,
    performedBy?: string,
    entityManager?: any,
  ): Promise<LicenseEntity> {
    try {
      const licenseRepo = entityManager
        ? entityManager.getRepository(LicenseEntity)
        : this.licenseRepository;

      // Get license using the same entityManager if provided
      const license = await licenseRepo
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .leftJoinAndSelect('license.user', 'user')
        .leftJoinAndSelect('license.quotas', 'quotas')
        .where('license.id = :licenseId', { licenseId })
        .andWhere('subscription.organizationId = :organizationId', { organizationId })
        .getOne();

      if (!license) {
        throw new NotFoundException(`License ${licenseId} not found or not accessible`);
      }

      if (!license.userId) {
        throw new BadRequestException(`License ${licenseId} is not assigned to any user`);
      }

      const previousUserId = license.userId;

      // Unassign license
      license.userId = null;
      license.assignedAt = null;
      license.status = LicenseStatus.ACTIVE;

      const savedLicense = await licenseRepo.save(license);

      // Handle credit cleanup if needed
      await this.handleLicenseUnassignment(license, previousUserId, entityManager);

      this.logger.log(`Unassigned license ${licenseId} from user ${previousUserId}`);
      return savedLicense;
    } catch (error) {
      this.logger.error(`Error unassigning license: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get license summary for organization
   */
  async getLicenseSummaryForOrganization(organizationId: string): Promise<{
    totalLicenses: number;
    assignedLicenses: number;
    availableLicenses: number;
    activeLicenses: number;
    inactiveLicenses: number;
    utilizationPercentage: number;
    statusBreakdown: Record<string, number>;
  }> {
    try {
      const licenses = await this.licenseRepository
        .createQueryBuilder('license')
        .leftJoin('license.subscription', 'subscription')
        .where('subscription.organizationId = :organizationId', { organizationId })
        .getMany();

      const totalLicenses = licenses.length;
      const assignedLicenses = licenses.filter((l) => l.userId).length;
      const availableLicenses = totalLicenses - assignedLicenses;
      const activeLicenses = licenses.filter((l) => l.status === LicenseStatus.ACTIVE).length;
      const inactiveLicenses = licenses.filter((l) => l.status === LicenseStatus.INACTIVE).length;
      const utilizationPercentage =
        totalLicenses > 0 ? (assignedLicenses / totalLicenses) * 100 : 0;

      const statusBreakdown = licenses.reduce(
        (acc, license) => {
          acc[license.status] = (acc[license.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      return {
        totalLicenses,
        assignedLicenses,
        availableLicenses,
        activeLicenses,
        inactiveLicenses,
        utilizationPercentage: Math.round(utilizationPercentage * 100) / 100,
        statusBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting license summary: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get license history for user
   */
  async getLicenseHistoryForUser(userId: string): Promise<{
    transfers: LicenseTransferEntity[];
    currentLicenses: LicenseEntity[];
    totalTransfers: number;
  }> {
    try {
      // Get transfer history
      const transfers = await this.licenseTransferRepository
        .createQueryBuilder('transfer')
        .leftJoinAndSelect('transfer.license', 'license')
        .leftJoinAndSelect('transfer.fromUser', 'fromUser')
        .leftJoinAndSelect('transfer.toUser', 'toUser')
        .leftJoinAndSelect('transfer.performedByUser', 'performedByUser')
        .where('transfer.fromUserId = :userId OR transfer.toUserId = :userId', { userId })
        .orderBy('transfer.createdAt', 'DESC')
        .getMany();

      // Get current licenses
      const currentLicenses = await this.licenseRepository
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.subscription', 'subscription')
        .where('license.userId = :userId', { userId })
        .getMany();

      return {
        transfers,
        currentLicenses,
        totalTransfers: transfers.length,
      };
    } catch (error) {
      this.logger.error(`Error getting license history: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available license count for organization
   */
  async getAvailableLicenseCount(organizationId: string): Promise<number> {
    try {
      const count = await this.licenseRepository
        .createQueryBuilder('license')
        .leftJoin('license.subscription', 'subscription')
        .where('subscription.organizationId = :organizationId', { organizationId })
        .andWhere('license.userId IS NULL')
        .andWhere('license.status = :status', { status: LicenseStatus.ACTIVE })
        .getCount();

      return count;
    } catch (error) {
      this.logger.error(`Error getting available license count: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get license count for subscription
   */
  async getLicenseCountForSubscription(subscriptionId: string): Promise<number> {
    try {
      const count = await this.licenseRepository.count({
        where: { subscriptionId },
      });
      return count;
    } catch (error) {
      this.logger.error(
        `Error getting license count for subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate that user belongs to organization
   */
  async validateUserInOrganization(userId: string, organizationId: string): Promise<void> {
    try {
      const user = await this.dataSource.getRepository(UserEntity).findOne({
        where: { id: userId, organizationId },
      });

      if (!user) {
        throw new BadRequestException(`User ${userId} not found in organization ${organizationId}`);
      }
    } catch (error) {
      this.logger.error(`Error validating user in organization: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle license unassignment cleanup
   */
  private async handleLicenseUnassignment(
    license: LicenseEntity,
    previousUserId: string,
    entityManager?: any,
  ): Promise<void> {
    try {
      // Handle credit cleanup or transfer logic here
      // This could involve moving credits back to organization pool
      // or handling them according to business rules

      this.logger.log(
        `Handled license unassignment cleanup for license ${license.id} from user ${previousUserId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling license unassignment cleanup: ${error.message}`,
        error.stack,
      );
      // Don't throw here as this is cleanup - log and continue
    }
  }

  /**
   * Validate transfer limits for user
   */
  private async validateTransferLimits(userId: string, entityManager?: any): Promise<void> {
    const transferRepo = entityManager
      ? entityManager.getRepository(LicenseTransferEntity)
      : this.licenseTransferRepository;

    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const transfersThisMonth = await transferRepo.count({
      where: {
        fromUserId: userId,
        createdAt: {
          $gte: startOfMonth,
        } as any,
      },
    });

    if (transfersThisMonth >= SUBSCRIPTION_SYSTEM_CONFIG.MAX_LICENSE_TRANSFERS_PER_MONTH) {
      throw new BadRequestException(
        `User has exceeded the maximum number of license transfers per month (${SUBSCRIPTION_SYSTEM_CONFIG.MAX_LICENSE_TRANSFERS_PER_MONTH})`,
      );
    }
  }

  /**
   * Get credit balances for license
   */
  private async getCreditBalancesForLicense(licenseId: string, entityManager?: any): Promise<any> {
    // Implementation to get current credit balances
    // This would query user_quotas table for the license
    return {};
  }

  /**
   * Transfer user quotas between users
   */
  private async transferUserQuotas(
    fromUserId: string,
    toUserId: string,
    licenseId: string,
    entityManager?: any,
  ): Promise<void> {
    // Implementation to transfer quotas
    // This would update user_quotas table to change userId but keep usage history
  }

  /**
   * Remove user quotas for license
   */
  private async removeUserQuotasForLicense(licenseId: string, entityManager?: any): Promise<void> {
    // Implementation to remove user quotas when license is unassigned
  }
}
