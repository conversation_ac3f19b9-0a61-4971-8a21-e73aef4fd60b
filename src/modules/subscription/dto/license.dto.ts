import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsUUID,
  IsInt,
  Min,
  Max,
  IsNotEmpty,
  IsNumber,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { LicenseStatus } from '../entities/license.entity';

export class AssignLicenseDto {
  @ApiProperty({
    description: 'User ID to assign the license to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;
}

export class TransferLicenseDto {
  @ApiProperty({
    description: 'User ID to transfer the license to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  toUserId: string;

  @ApiPropertyOptional({
    description: 'Reason for the license transfer',
    example: 'User role change',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

export class LicenseQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by license status',
    enum: LicenseStatus,
    example: LicenseStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(LicenseStatus)
  status?: LicenseStatus;

  @ApiPropertyOptional({
    description: 'Filter by assigned user ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

export class UserDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'User full name' })
  fullName: string;

  @ApiProperty({ description: 'User username' })
  username: string;
}

export class SubscriptionDto {
  @ApiProperty({ description: 'Subscription ID' })
  id: string;

  @ApiProperty({ description: 'Plan name' })
  planName: string;

  @ApiProperty({ description: 'Subscription status' })
  status: string;
}

export class LicenseResponseDto {
  @ApiProperty({ description: 'License ID' })
  id: string;

  @ApiProperty({ description: 'Subscription ID' })
  subscriptionId: string;

  @ApiProperty({ description: 'User ID (if assigned)' })
  userId: string | null;

  @ApiProperty({ description: 'License status', enum: LicenseStatus })
  status: LicenseStatus;

  @ApiProperty({ description: 'Date when license was assigned' })
  assignedAt: Date | null;

  @ApiProperty({ description: 'License creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'License last update date' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Assigned user details', type: UserDto })
  user?: UserDto;

  @ApiPropertyOptional({ description: 'Subscription details', type: SubscriptionDto })
  subscription?: SubscriptionDto;
}

export class LicenseListResponseDto {
  @ApiProperty({ description: 'List of licenses', type: [LicenseResponseDto] })
  licenses: LicenseResponseDto[];

  @ApiProperty({ description: 'Total number of licenses' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;
}

export class LicenseSummaryDto {
  @ApiProperty({ description: 'Total number of licenses' })
  totalLicenses: number;

  @ApiProperty({ description: 'Number of assigned licenses' })
  assignedLicenses: number;

  @ApiProperty({ description: 'Number of available licenses' })
  availableLicenses: number;

  @ApiProperty({ description: 'Number of active licenses' })
  activeLicenses: number;

  @ApiProperty({ description: 'Number of inactive licenses' })
  inactiveLicenses: number;

  @ApiProperty({ description: 'License utilization percentage' })
  utilizationPercentage: number;

  @ApiProperty({ description: 'Breakdown by status' })
  statusBreakdown: {
    [key in LicenseStatus]: number;
  };
}

export class LicenseTransferHistoryDto {
  @ApiProperty({ description: 'Transfer ID' })
  id: string;

  @ApiProperty({ description: 'License ID' })
  licenseId: string;

  @ApiProperty({ description: 'From user ID' })
  fromUserId: string;

  @ApiProperty({ description: 'To user ID' })
  toUserId: string;

  @ApiProperty({ description: 'Transfer reason' })
  reason: string;

  @ApiProperty({ description: 'User who performed the transfer' })
  performedBy: string;

  @ApiProperty({ description: 'Transfer date' })
  createdAt: Date;

  @ApiPropertyOptional({ description: 'From user details', type: UserDto })
  fromUser?: UserDto;

  @ApiPropertyOptional({ description: 'To user details', type: UserDto })
  toUser?: UserDto;

  @ApiPropertyOptional({ description: 'Performed by user details', type: UserDto })
  performedByUser?: UserDto;
}

export class LicenseHistoryResponseDto {
  @ApiProperty({ description: 'License transfers', type: [LicenseTransferHistoryDto] })
  transfers: LicenseTransferHistoryDto[];

  @ApiProperty({ description: 'Current license assignments', type: [LicenseResponseDto] })
  currentLicenses: LicenseResponseDto[];

  @ApiProperty({ description: 'Total transfers count' })
  totalTransfers: number;
}

export class AddLicensesToSubscriptionDto {
  @ApiProperty({ description: 'Subscription ID to add licenses to' })
  @IsNotEmpty()
  @IsString()
  subscriptionId: string;

  @ApiProperty({ description: 'Number of additional licenses to add', minimum: 1 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  additionalLicenseCount: number;
}

export class AddLicensesResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({
    description: 'Response data',
    type: 'object',
    properties: {
      message: { type: 'string' },
      newLicenses: { type: 'array', items: { $ref: '#/components/schemas/LicenseResponseDto' } },
      totalLicenseCount: { type: 'number' },
      subscription: { type: 'object', additionalProperties: true },
      needsNewPaymentIntent: { type: 'boolean' },
      paymentIntentCanceled: { type: 'boolean' },
    },
  })
  data: {
    message: string;
    newLicenses: LicenseResponseDto[];
    totalLicenseCount: number;
    subscription: any;
    needsNewPaymentIntent: boolean;
    paymentIntentCanceled: boolean;
  };
}
